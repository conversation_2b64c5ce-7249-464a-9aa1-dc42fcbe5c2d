package com.workly.app.services

import com.workly.app.data.model.Note
import com.workly.app.data.model.Shift

interface NotificationService {
    suspend fun scheduleShiftReminder(shift: Shift, reminderTime: Long)
    suspend fun scheduleNoteReminder(note: Note)
    suspend fun cancelShiftReminder(shiftId: String)
    suspend fun cancelNoteReminder(noteId: String)
    suspend fun cancelAllReminders()
    suspend fun showInstantNotification(title: String, message: String, channelId: String)
    suspend fun hasNotificationPermission(): Boolean
    suspend fun requestNotificationPermission(): Boolean
    suspend fun updateNotificationSettings(soundEnabled: Boolean, vibrationEnabled: Boolean)
}
