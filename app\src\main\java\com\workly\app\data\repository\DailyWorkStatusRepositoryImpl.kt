package com.workly.app.data.repository

import com.workly.app.data.database.dao.DailyWorkStatusDao
import com.workly.app.data.model.DailyWorkStatus
import com.workly.app.data.model.WorkStatus
import kotlinx.coroutines.flow.Flow
import java.time.LocalDate
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class DailyWorkStatusRepositoryImpl @Inject constructor(
    private val dailyWorkStatusDao: DailyWorkStatusDao
) : DailyWorkStatusRepository {

    override fun getAllDailyWorkStatus(): Flow<List<DailyWorkStatus>> {
        return dailyWorkStatusDao.getAllDailyWorkStatus()
    }

    override suspend fun getDailyWorkStatusByDate(date: LocalDate): DailyWorkStatus? {
        return dailyWorkStatusDao.getDailyWorkStatusByDate(date)
    }

    override fun getDailyWorkStatusByDateFlow(date: LocalDate): Flow<DailyWorkStatus?> {
        return dailyWorkStatusDao.getDailyWorkStatusByDateFlow(date)
    }

    override suspend fun insertDailyWorkStatus(status: DailyWorkStatus) {
        dailyWorkStatusDao.insertDailyWorkStatus(status)
    }

    override suspend fun insertDailyWorkStatuses(statuses: List<DailyWorkStatus>) {
        dailyWorkStatusDao.insertDailyWorkStatuses(statuses)
    }

    override suspend fun updateDailyWorkStatus(status: DailyWorkStatus) {
        dailyWorkStatusDao.updateDailyWorkStatus(status)
    }

    override suspend fun deleteDailyWorkStatus(status: DailyWorkStatus) {
        dailyWorkStatusDao.deleteDailyWorkStatus(status)
    }

    override suspend fun deleteDailyWorkStatusByDate(date: LocalDate) {
        dailyWorkStatusDao.deleteDailyWorkStatusByDate(date)
    }

    override suspend fun deleteAllDailyWorkStatus() {
        dailyWorkStatusDao.deleteAllDailyWorkStatus()
    }

    override suspend fun getStatusForDateRange(startDate: LocalDate, endDate: LocalDate): List<DailyWorkStatus> {
        return dailyWorkStatusDao.getStatusForDateRange(startDate, endDate)
    }

    override fun getStatusForDateRangeFlow(startDate: LocalDate, endDate: LocalDate): Flow<List<DailyWorkStatus>> {
        return dailyWorkStatusDao.getStatusForDateRangeFlow(startDate, endDate)
    }

    override suspend fun getStatusByType(status: WorkStatus): List<DailyWorkStatus> {
        return dailyWorkStatusDao.getStatusByType(status)
    }

    override fun getStatusByTypeFlow(status: WorkStatus): Flow<List<DailyWorkStatus>> {
        return dailyWorkStatusDao.getStatusByTypeFlow(status)
    }

    override suspend fun getManualOverrides(): List<DailyWorkStatus> {
        return dailyWorkStatusDao.getManualOverrides()
    }

    override fun getManualOverridesFlow(): Flow<List<DailyWorkStatus>> {
        return dailyWorkStatusDao.getManualOverridesFlow()
    }

    override suspend fun getHolidayWork(): List<DailyWorkStatus> {
        return dailyWorkStatusDao.getHolidayWork()
    }

    override fun getHolidayWorkFlow(): Flow<List<DailyWorkStatus>> {
        return dailyWorkStatusDao.getHolidayWorkFlow()
    }

    override suspend fun getStatusByShiftId(shiftId: String): List<DailyWorkStatus> {
        return dailyWorkStatusDao.getStatusByShiftId(shiftId)
    }

    override fun getStatusByShiftIdFlow(shiftId: String): Flow<List<DailyWorkStatus>> {
        return dailyWorkStatusDao.getStatusByShiftIdFlow(shiftId)
    }

    override suspend fun getRecentStatus(fromDate: LocalDate, limit: Int): List<DailyWorkStatus> {
        return dailyWorkStatusDao.getRecentStatus(fromDate, limit)
    }

    override fun getRecentStatusFlow(fromDate: LocalDate, limit: Int): Flow<List<DailyWorkStatus>> {
        return dailyWorkStatusDao.getRecentStatusFlow(fromDate, limit)
    }

    override suspend fun getStatusCountForPeriod(status: WorkStatus, startDate: LocalDate, endDate: LocalDate): Int {
        return dailyWorkStatusDao.getStatusCountForPeriod(status, startDate, endDate)
    }

    override suspend fun getTotalHoursForPeriod(startDate: LocalDate, endDate: LocalDate): Double? {
        return dailyWorkStatusDao.getTotalHoursForPeriod(startDate, endDate)
    }

    override suspend fun getTotalOvertimeHoursForPeriod(startDate: LocalDate, endDate: LocalDate): Double? {
        return dailyWorkStatusDao.getTotalOvertimeHoursForPeriod(startDate, endDate)
    }

    override suspend fun getAverageLateMinutesForPeriod(startDate: LocalDate, endDate: LocalDate): Double? {
        return dailyWorkStatusDao.getAverageLateMinutesForPeriod(startDate, endDate)
    }
}
