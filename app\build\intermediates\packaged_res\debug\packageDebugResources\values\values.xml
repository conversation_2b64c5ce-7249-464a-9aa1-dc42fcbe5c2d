<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <color name="background_dark">#121212</color>
    <color name="background_light">#FFFFFF</color>
    <color name="black">#000000</color>
    <color name="blue_100">#BBDEFB</color>
    <color name="blue_200">#90CAF9</color>
    <color name="blue_300">#64B5F6</color>
    <color name="blue_400">#42A5F5</color>
    <color name="blue_50">#E3F2FD</color>
    <color name="blue_500">#2196F3</color>
    <color name="blue_600">#1E88E5</color>
    <color name="blue_700">#1976D2</color>
    <color name="blue_800">#1565C0</color>
    <color name="blue_900">#0D47A1</color>
    <color name="gray_100">#F5F5F5</color>
    <color name="gray_200">#EEEEEE</color>
    <color name="gray_300">#E0E0E0</color>
    <color name="gray_400">#BDBDBD</color>
    <color name="gray_50">#FAFAFA</color>
    <color name="gray_500">#9E9E9E</color>
    <color name="gray_600">#757575</color>
    <color name="gray_700">#616161</color>
    <color name="gray_800">#424242</color>
    <color name="gray_900">#212121</color>
    <color name="green_500">#4CAF50</color>
    <color name="orange_500">#FF9800</color>
    <color name="red_500">#F44336</color>
    <color name="surface_dark">#1E1E1E</color>
    <color name="surface_light">#FFFFFF</color>
    <color name="white">#FFFFFF</color>
    <color name="yellow_500">#FFEB3B</color>
    <string name="add">Thêm</string>
    <string name="app_name">Workly</string>
    <string name="attendance_check_in">Vào làm</string>
    <string name="attendance_check_out">Ra về</string>
    <string name="attendance_complete">Hoàn thành</string>
    <string name="attendance_go_work">Đi làm</string>
    <string name="attendance_history_title">Lịch sử chấm công</string>
    <string name="attendance_punch">Chấm công</string>
    <string name="cancel">Hủy</string>
    <string name="check_in">Vào làm</string>
    <string name="check_out">Ra về</string>
    <string name="completed">Hoàn thành</string>
    <string name="delete">Xóa</string>
    <string name="edit">Sửa</string>
    <string name="error">Lỗi</string>
    <string name="go_work">Đi làm</string>
    <string name="loading">Đang tải...</string>
    <string name="notes_title">Ghi chú</string>
    <string name="notification_location_service_content">Đang theo dõi vị trí cho chấm công tự động</string>
    <string name="notification_location_service_title">Dịch vụ vị trí</string>
    <string name="notification_note_reminder_title">Nhắc nhở ghi chú</string>
    <string name="notification_shift_reminder_title">Nhắc nhở ca làm việc</string>
    <string name="ok">OK</string>
    <string name="permission_location_message">Ứng dụng cần quyền truy cập vị trí để xác định vị trí nhà và công ty cho tính năng cảnh báo thời tiết và chấm công tự động.</string>
    <string name="permission_location_title">Quyền truy cập vị trí</string>
    <string name="permission_notification_message">Ứng dụng cần quyền gửi thông báo để nhắc nhở về ca làm việc và ghi chú.</string>
    <string name="permission_notification_title">Quyền thông báo</string>
    <string name="ready_to_work">Sẵn sàng làm việc</string>
    <string name="retry">Thử lại</string>
    <string name="save">Lưu</string>
    <string name="status_absent">Vắng mặt</string>
    <string name="status_completed">Hoàn thành</string>
    <string name="status_day_off">Nghỉ</string>
    <string name="status_early">Về sớm</string>
    <string name="status_late">Đi muộn</string>
    <string name="status_pending">Chờ xử lý</string>
    <string name="title_home">Trang chủ</string>
    <string name="title_notes">Ghi chú</string>
    <string name="title_settings">Cài đặt</string>
    <string name="title_shifts">Ca làm việc</string>
    <string name="title_statistics">Thống kê</string>
    <string name="weather_cloudy">Có mây</string>
    <string name="weather_rainy">Mưa</string>
    <string name="weather_stormy">Bão</string>
    <string name="weather_sunny">Nắng</string>
    <style name="Theme.Workly" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/blue_500</item>
        <item name="colorPrimaryVariant">@color/blue_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/blue_200</item>
        <item name="colorSecondaryVariant">@color/blue_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor" ns1:targetApi="l">?attr/colorPrimaryVariant</item>
        
    </style>
    <style name="Theme.Workly.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar"/>
    <style name="Theme.Workly.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.Workly.PopupOverlay" parent="ThemeOverlay.AppCompat.Light"/>
</resources>