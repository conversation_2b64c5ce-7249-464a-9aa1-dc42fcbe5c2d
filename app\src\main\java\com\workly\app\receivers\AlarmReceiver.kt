package com.workly.app.receivers

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import com.workly.app.R
import com.workly.app.WorklyApplication
import com.workly.app.ui.MainActivity
import android.app.PendingIntent

class AlarmReceiver : BroadcastReceiver() {

    override fun onReceive(context: Context, intent: Intent) {
        when (intent.action) {
            "SHIFT_REMINDER" -> handleShiftReminder(context, intent)
            "NOTE_REMINDER" -> handleNoteReminder(context, intent)
        }
    }

    private fun handleShiftReminder(context: Context, intent: Intent) {
        val shiftId = intent.getStringExtra("shift_id") ?: return
        val shiftName = intent.getStringExtra("shift_name") ?: return
        val title = intent.getStringExtra("title") ?: "Nhắc nhở ca làm việc"
        val message = intent.getStringExtra("message") ?: "Ca $shiftName sắp bắt đầu"

        showNotification(context, title, message, WorklyApplication.SHIFT_REMINDER_CHANNEL_ID)
    }

    private fun handleNoteReminder(context: Context, intent: Intent) {
        val noteId = intent.getStringExtra("note_id") ?: return
        val noteTitle = intent.getStringExtra("note_title") ?: return
        val title = intent.getStringExtra("title") ?: "Nhắc nhở ghi chú"
        val message = intent.getStringExtra("message") ?: noteTitle

        showNotification(context, title, message, WorklyApplication.NOTE_REMINDER_CHANNEL_ID)
    }

    private fun showNotification(context: Context, title: String, message: String, channelId: String) {
        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }

        val pendingIntent = PendingIntent.getActivity(
            context,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val notification = NotificationCompat.Builder(context, channelId)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle(title)
            .setContentText(message)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setDefaults(NotificationCompat.DEFAULT_ALL)
            .build()

        val notificationManager = NotificationManagerCompat.from(context)
        notificationManager.notify(System.currentTimeMillis().toInt(), notification)
    }
}
