#Thu Jul 03 05:43:41 ICT 2025
com.workly.app-main-65\:/drawable/bg_chip_outline.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_chip_outline.xml.flat
com.workly.app-main-65\:/drawable/ic_alarm_16.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_alarm_16.xml.flat
com.workly.app-main-65\:/drawable/ic_analytics_24.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_analytics_24.xml.flat
com.workly.app-main-65\:/drawable/ic_home_24.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_home_24.xml.flat
com.workly.app-main-65\:/drawable/ic_launcher_background.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_background.xml.flat
com.workly.app-main-65\:/drawable/ic_launcher_foreground.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_foreground.xml.flat
com.workly.app-main-65\:/drawable/ic_location_on_16.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_location_on_16.xml.flat
com.workly.app-main-65\:/drawable/ic_note_16.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_note_16.xml.flat
com.workly.app-main-65\:/drawable/ic_note_24.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_note_24.xml.flat
com.workly.app-main-65\:/drawable/ic_notification.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_notification.xml.flat
com.workly.app-main-65\:/drawable/ic_schedule_16.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_schedule_16.xml.flat
com.workly.app-main-65\:/drawable/ic_schedule_24.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_schedule_24.xml.flat
com.workly.app-main-65\:/drawable/ic_settings_24.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_settings_24.xml.flat
com.workly.app-main-65\:/drawable/ic_sunny.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_sunny.xml.flat
com.workly.app-main-65\:/menu/bottom_nav_menu.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_bottom_nav_menu.xml.flat
com.workly.app-main-65\:/mipmap-anydpi-v26/ic_launcher.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher.xml.flat
com.workly.app-main-65\:/mipmap-anydpi-v26/ic_launcher_round.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher_round.xml.flat
com.workly.app-main-65\:/mipmap-hdpi/ic_launcher.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher.xml.flat
com.workly.app-main-65\:/mipmap-hdpi/ic_launcher_round.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher_round.xml.flat
com.workly.app-main-65\:/mipmap-mdpi/ic_launcher.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher.xml.flat
com.workly.app-main-65\:/mipmap-mdpi/ic_launcher_round.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher_round.xml.flat
com.workly.app-main-65\:/mipmap-xhdpi/ic_launcher.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher.xml.flat
com.workly.app-main-65\:/mipmap-xhdpi/ic_launcher_round.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher_round.xml.flat
com.workly.app-main-65\:/mipmap-xxhdpi/ic_launcher.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher.xml.flat
com.workly.app-main-65\:/mipmap-xxhdpi/ic_launcher_round.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher_round.xml.flat
com.workly.app-main-65\:/mipmap-xxxhdpi/ic_launcher.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher.xml.flat
com.workly.app-main-65\:/mipmap-xxxhdpi/ic_launcher_round.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher_round.xml.flat
com.workly.app-main-65\:/navigation/mobile_navigation.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\navigation_mobile_navigation.xml.flat
com.workly.app-main-65\:/xml/backup_rules.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_backup_rules.xml.flat
com.workly.app-main-65\:/xml/data_extraction_rules.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_data_extraction_rules.xml.flat
com.workly.app-mergeDebugResources-62\:/layout/activity_main.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_main.xml.flat
com.workly.app-mergeDebugResources-62\:/layout/fragment_home.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_home.xml.flat
com.workly.app-mergeDebugResources-62\:/layout/fragment_notes.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_notes.xml.flat
com.workly.app-mergeDebugResources-62\:/layout/fragment_settings.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_settings.xml.flat
com.workly.app-mergeDebugResources-62\:/layout/fragment_shifts.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_shifts.xml.flat
com.workly.app-mergeDebugResources-62\:/layout/fragment_statistics.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_statistics.xml.flat
com.workly.app-mergeDebugResources-62\:/layout/item_attendance_log.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_attendance_log.xml.flat
com.workly.app-mergeDebugResources-62\:/layout/item_note.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_note.xml.flat
