package com.workly.app.services

import com.workly.app.data.model.WeatherData
import com.workly.app.data.model.LocationCoordinate

interface WeatherService {
    suspend fun getCurrentWeather(): WeatherData?
    suspend fun getWeatherForLocation(location: LocationCoordinate): WeatherData?
    suspend fun getWeatherForecast(location: LocationCoordinate, days: Int = 5): WeatherData?
    suspend fun checkWeatherWarnings(homeLocation: LocationCoordinate?, workLocation: LocationCoordinate?): List<com.workly.app.data.model.WeatherWarning>
    suspend fun refreshWeatherData()
}
