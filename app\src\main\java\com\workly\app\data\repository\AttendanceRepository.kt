package com.workly.app.data.repository

import com.workly.app.data.model.AttendanceLog
import com.workly.app.data.model.AttendanceType
import kotlinx.coroutines.flow.Flow
import java.time.LocalDate
import java.time.LocalDateTime

interface AttendanceRepository {
    fun getAllAttendanceLogs(): Flow<List<AttendanceLog>>
    suspend fun getAttendanceLogById(id: String): AttendanceLog?
    suspend fun insertAttendanceLog(log: AttendanceLog)
    suspend fun insertAttendanceLogs(logs: List<AttendanceLog>)
    suspend fun updateAttendanceLog(log: AttendanceLog)
    suspend fun deleteAttendanceLog(log: AttendanceLog)
    suspend fun deleteAttendanceLogById(id: String)
    suspend fun deleteAllAttendanceLogs()
    suspend fun getLogsForDate(date: LocalDate): List<AttendanceLog>
    fun getLogsForDateFlow(date: LocalDate): Flow<List<AttendanceLog>>
    suspend fun getLogsForDateRange(startDate: LocalDate, endDate: LocalDate): List<AttendanceLog>
    fun getLogsForDateRangeFlow(startDate: LocalDate, endDate: LocalDate): Flow<List<AttendanceLog>>
    suspend fun getLogsByShiftId(shiftId: String): List<AttendanceLog>
    fun getLogsByShiftIdFlow(shiftId: String): Flow<List<AttendanceLog>>
    suspend fun getLogsByType(type: AttendanceType): List<AttendanceLog>
    suspend fun getRecentLogs(fromDate: LocalDateTime, limit: Int): List<AttendanceLog>
    fun getRecentLogsFlow(fromDate: LocalDateTime, limit: Int): Flow<List<AttendanceLog>>
    suspend fun getAutoGeneratedLogs(): List<AttendanceLog>
    suspend fun getManualLogs(): List<AttendanceLog>
    suspend fun getLogsCount(): Int
    suspend fun getLogsCountForDate(date: LocalDate): Int
}
