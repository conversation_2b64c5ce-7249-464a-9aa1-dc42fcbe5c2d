{"logs": [{"outputFile": "com.workly.app-mergeDebugResources-61:/values-sv/values-sv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f72d64ff0a85ad35d4f32776e9fe964a\\transformed\\material-1.11.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,266,365,457,538,640,720,818,940,1019,1082,1174,1238,1298,1390,1455,1518,1580,1647,1711,1765,1870,1929,1990,2044,2113,2232,2315,2399,2535,2614,2698,2820,2906,2984,3038,3089,3155,3224,3298,3387,3463,3535,3612,3683,3757,3868,3959,4038,4125,4213,4285,4359,4444,4495,4574,4641,4722,4806,4868,4932,4995,5063,5170,5269,5368,5463,5521,5576", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,98,91,80,101,79,97,121,78,62,91,63,59,91,64,62,61,66,63,53,104,58,60,53,68,118,82,83,135,78,83,121,85,77,53,50,65,68,73,88,75,71,76,70,73,110,90,78,86,87,71,73,84,50,78,66,80,83,61,63,62,67,106,98,98,94,57,54,77", "endOffsets": "261,360,452,533,635,715,813,935,1014,1077,1169,1233,1293,1385,1450,1513,1575,1642,1706,1760,1865,1924,1985,2039,2108,2227,2310,2394,2530,2609,2693,2815,2901,2979,3033,3084,3150,3219,3293,3382,3458,3530,3607,3678,3752,3863,3954,4033,4120,4208,4280,4354,4439,4490,4569,4636,4717,4801,4863,4927,4990,5058,5165,5264,5363,5458,5516,5571,5649"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,67,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2989,3088,3180,3261,3363,4171,4269,4391,6747,6898,6990,7054,7114,7206,7271,7334,7396,7463,7527,7581,7686,7745,7806,7860,7929,8048,8131,8215,8351,8430,8514,8636,8722,8800,8854,8905,8971,9040,9114,9203,9279,9351,9428,9499,9573,9684,9775,9854,9941,10029,10101,10175,10260,10311,10390,10457,10538,10622,10684,10748,10811,10879,10986,11085,11184,11279,11337,11698", "endLines": "5,33,34,35,36,37,45,46,47,67,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,130", "endColumns": "12,98,91,80,101,79,97,121,78,62,91,63,59,91,64,62,61,66,63,53,104,58,60,53,68,118,82,83,135,78,83,121,85,77,53,50,65,68,73,88,75,71,76,70,73,110,90,78,86,87,71,73,84,50,78,66,80,83,61,63,62,67,106,98,98,94,57,54,77", "endOffsets": "311,3083,3175,3256,3358,3438,4264,4386,4465,6805,6985,7049,7109,7201,7266,7329,7391,7458,7522,7576,7681,7740,7801,7855,7924,8043,8126,8210,8346,8425,8509,8631,8717,8795,8849,8900,8966,9035,9109,9198,9274,9346,9423,9494,9568,9679,9770,9849,9936,10024,10096,10170,10255,10306,10385,10452,10533,10617,10679,10743,10806,10874,10981,11080,11179,11274,11332,11387,11771"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\3678371e4b9f291b0b6ff6bca1ac707a\\transformed\\play-services-base-18.1.0\\res\\values-sv\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,449,572,678,815,936,1055,1155,1299,1403,1561,1685,1835,1987,2049,2108", "endColumns": "102,152,122,105,136,120,118,99,143,103,157,123,149,151,61,58,74", "endOffsets": "295,448,571,677,814,935,1054,1154,1298,1402,1560,1684,1834,1986,2048,2107,2182"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4470,4577,4734,4861,4971,5112,5237,5360,5612,5760,5868,6030,6158,6312,6468,6534,6597", "endColumns": "106,156,126,109,140,124,122,103,147,107,161,127,153,155,65,62,78", "endOffsets": "4572,4729,4856,4966,5107,5232,5355,5459,5755,5863,6025,6153,6307,6463,6529,6592,6671"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\240c634c8d2ef38e2610db507fc6f6e4\\transformed\\play-services-basement-18.1.0\\res\\values-sv\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "143", "endOffsets": "338"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5464", "endColumns": "147", "endOffsets": "5607"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dbb4439497655819a5bbf0c365938793\\transformed\\navigation-ui-2.7.6\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,118", "endOffsets": "159,278"}, "to": {"startLines": "127,128", "startColumns": "4,4", "startOffsets": "11392,11501", "endColumns": "108,118", "endOffsets": "11496,11615"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8af33194f3dfc2f8bc08d7753e6a12a4\\transformed\\core-1.12.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,449,557,662,783", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "145,247,345,444,552,657,778,879"}, "to": {"startLines": "38,39,40,41,42,43,44,133", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3443,3538,3640,3738,3837,3945,4050,11993", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "3533,3635,3733,3832,3940,4045,4166,12089"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\14e1ed44b3025a4e79fd4f5fe03e4452\\transformed\\appcompat-1.6.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,422,506,608,721,798,873,966,1061,1156,1250,1352,1447,1544,1642,1738,1831,1911,2017,2116,2212,2317,2420,2522,2676,2778", "endColumns": "102,102,110,83,101,112,76,74,92,94,94,93,101,94,96,97,95,92,79,105,98,95,104,102,101,153,101,79", "endOffsets": "203,306,417,501,603,716,793,868,961,1056,1151,1245,1347,1442,1539,1637,1733,1826,1906,2012,2111,2207,2312,2415,2517,2671,2773,2853"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,132", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "316,419,522,633,717,819,932,1009,1084,1177,1272,1367,1461,1563,1658,1755,1853,1949,2042,2122,2228,2327,2423,2528,2631,2733,2887,11913", "endColumns": "102,102,110,83,101,112,76,74,92,94,94,93,101,94,96,97,95,92,79,105,98,95,104,102,101,153,101,79", "endOffsets": "414,517,628,712,814,927,1004,1079,1172,1267,1362,1456,1558,1653,1750,1848,1944,2037,2117,2223,2322,2418,2523,2626,2728,2882,2984,11988"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dcc97860fb5a05a9300bca7d380ad952\\transformed\\preference-1.2.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,264,342,479,648,727", "endColumns": "70,87,77,136,168,78,75", "endOffsets": "171,259,337,474,643,722,798"}, "to": {"startLines": "66,68,129,131,134,135,136", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6676,6810,11620,11776,12094,12263,12342", "endColumns": "70,87,77,136,168,78,75", "endOffsets": "6742,6893,11693,11908,12258,12337,12413"}}]}]}