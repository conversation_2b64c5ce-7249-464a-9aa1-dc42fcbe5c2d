package com.workly.app

import android.app.Application
import android.app.NotificationChannel
import android.app.NotificationManager
import android.os.Build
import androidx.hilt.work.HiltWorkerFactory
import androidx.work.Configuration
import dagger.hilt.android.HiltAndroidApp
import javax.inject.Inject

@HiltAndroidApp
class WorklyApplication : Application(), Configuration.Provider {

    @Inject
    lateinit var workerFactory: HiltWorkerFactory

    override fun onCreate() {
        super.onCreate()
        createNotificationChannels()
    }

    override fun getWorkManagerConfiguration(): Configuration {
        return Configuration.Builder()
            .setWorkerFactory(workerFactory)
            .build()
    }

    private fun createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = getSystemService(NotificationManager::class.java)

            // Shift Reminder Channel
            val shiftChannel = NotificationChannel(
                SHIFT_REMINDER_CHANNEL_ID,
                "Nhắc nhở ca làm việc",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Thông báo nhắc nhở về ca làm việc"
                enableVibration(true)
                setShowBadge(true)
            }

            // Note Reminder Channel
            val noteChannel = NotificationChannel(
                NOTE_REMINDER_CHANNEL_ID,
                "Nhắc nhở ghi chú",
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = "Thông báo nhắc nhở về ghi chú"
                enableVibration(true)
                setShowBadge(true)
            }

            // Location Service Channel
            val locationChannel = NotificationChannel(
                LOCATION_SERVICE_CHANNEL_ID,
                "Dịch vụ vị trí",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Thông báo cho dịch vụ theo dõi vị trí"
                setShowBadge(false)
            }

            notificationManager.createNotificationChannels(
                listOf(shiftChannel, noteChannel, locationChannel)
            )
        }
    }

    companion object {
        const val SHIFT_REMINDER_CHANNEL_ID = "shift_reminder_channel"
        const val NOTE_REMINDER_CHANNEL_ID = "note_reminder_channel"
        const val LOCATION_SERVICE_CHANNEL_ID = "location_service_channel"
    }
}
