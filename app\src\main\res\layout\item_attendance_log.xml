<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="4dp"
    android:layout_marginVertical="2dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="12dp">

        <!-- Type Indicator -->
        <View
            android:id="@+id/view_type_indicator"
            android:layout_width="4dp"
            android:layout_height="0dp"
            android:background="@color/blue_500"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Type Text -->
        <TextView
            android:id="@+id/text_view_type"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
            android:textColor="?attr/colorOnSurface"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@id/text_view_time"
            app:layout_constraintStart_toEndOf="@id/view_type_indicator"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Vào làm" />

        <!-- Time -->
        <TextView
            android:id="@+id/text_view_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
            android:textColor="?attr/colorOnSurface"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="08:30" />

        <!-- Date -->
        <TextView
            android:id="@+id/text_view_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:textAppearance="@style/TextAppearance.Material3.BodySmall"
            android:textColor="?attr/colorOnSurfaceVariant"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/text_view_time"
            tools:text="02/07/2025" />

        <!-- Location -->
        <TextView
            android:id="@+id/text_view_location"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="8dp"
            android:drawablePadding="4dp"
            android:textAppearance="@style/TextAppearance.Material3.BodySmall"
            android:textColor="?attr/colorOnSurfaceVariant"
            android:visibility="gone"
            app:drawableStartCompat="@drawable/ic_location_on_16"
            app:layout_constraintEnd_toStartOf="@id/text_view_date"
            app:layout_constraintStart_toEndOf="@id/view_type_indicator"
            app:layout_constraintTop_toBottomOf="@id/text_view_type"
            tools:text="Văn phòng công ty"
            tools:visibility="visible" />

        <!-- Note -->
        <TextView
            android:id="@+id/text_view_note"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="8dp"
            android:drawablePadding="4dp"
            android:textAppearance="@style/TextAppearance.Material3.BodySmall"
            android:textColor="?attr/colorOnSurfaceVariant"
            android:visibility="gone"
            app:drawableStartCompat="@drawable/ic_note_16"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/view_type_indicator"
            app:layout_constraintTop_toBottomOf="@id/text_view_location"
            tools:text="Ghi chú về lần chấm công này"
            tools:visibility="visible" />

        <!-- Auto Generated Indicator -->
        <TextView
            android:id="@+id/text_view_auto_generated"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginTop="4dp"
            android:background="@drawable/bg_chip_outline"
            android:paddingHorizontal="8dp"
            android:paddingVertical="2dp"
            android:text="Tự động"
            android:textAppearance="@style/TextAppearance.Material3.LabelSmall"
            android:textColor="?attr/colorPrimary"
            android:visibility="gone"
            app:layout_constraintStart_toEndOf="@id/view_type_indicator"
            app:layout_constraintTop_toBottomOf="@id/text_view_note"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>
