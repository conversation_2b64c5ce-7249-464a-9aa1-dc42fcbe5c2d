package com.workly.app.data.database.dao

import androidx.room.*
import com.workly.app.data.model.DailyWorkStatus
import com.workly.app.data.model.WorkStatus
import kotlinx.coroutines.flow.Flow
import java.time.LocalDate

@Dao
interface DailyWorkStatusDao {
    
    @Query("SELECT * FROM daily_work_status ORDER BY date DESC")
    fun getAllDailyWorkStatus(): Flow<List<DailyWorkStatus>>
    
    @Query("SELECT * FROM daily_work_status WHERE date = :date")
    suspend fun getDailyWorkStatusByDate(date: LocalDate): DailyWorkStatus?
    
    @Query("SELECT * FROM daily_work_status WHERE date = :date")
    fun getDailyWorkStatusByDateFlow(date: LocalDate): Flow<DailyWorkStatus?>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertDailyWorkStatus(status: DailyWorkStatus)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertDailyWorkStatuses(statuses: List<DailyWorkStatus>)
    
    @Update
    suspend fun updateDailyWorkStatus(status: DailyWorkStatus)
    
    @Delete
    suspend fun deleteDailyWorkStatus(status: DailyWorkStatus)
    
    @Query("DELETE FROM daily_work_status WHERE date = :date")
    suspend fun deleteDailyWorkStatusByDate(date: LocalDate)
    
    @Query("DELETE FROM daily_work_status")
    suspend fun deleteAllDailyWorkStatus()
    
    // Get status for date range
    @Query("SELECT * FROM daily_work_status WHERE date BETWEEN :startDate AND :endDate ORDER BY date ASC")
    suspend fun getStatusForDateRange(startDate: LocalDate, endDate: LocalDate): List<DailyWorkStatus>
    
    @Query("SELECT * FROM daily_work_status WHERE date BETWEEN :startDate AND :endDate ORDER BY date ASC")
    fun getStatusForDateRangeFlow(startDate: LocalDate, endDate: LocalDate): Flow<List<DailyWorkStatus>>
    
    // Get status by work status type
    @Query("SELECT * FROM daily_work_status WHERE status = :status ORDER BY date DESC")
    suspend fun getStatusByType(status: WorkStatus): List<DailyWorkStatus>
    
    @Query("SELECT * FROM daily_work_status WHERE status = :status ORDER BY date DESC")
    fun getStatusByTypeFlow(status: WorkStatus): Flow<List<DailyWorkStatus>>
    
    // Get manual overrides
    @Query("SELECT * FROM daily_work_status WHERE isManualOverride = 1 ORDER BY date DESC")
    suspend fun getManualOverrides(): List<DailyWorkStatus>
    
    @Query("SELECT * FROM daily_work_status WHERE isManualOverride = 1 ORDER BY date DESC")
    fun getManualOverridesFlow(): Flow<List<DailyWorkStatus>>
    
    // Get holiday work
    @Query("SELECT * FROM daily_work_status WHERE isHolidayWork = 1 ORDER BY date DESC")
    suspend fun getHolidayWork(): List<DailyWorkStatus>
    
    @Query("SELECT * FROM daily_work_status WHERE isHolidayWork = 1 ORDER BY date DESC")
    fun getHolidayWorkFlow(): Flow<List<DailyWorkStatus>>
    
    // Get status by shift ID
    @Query("SELECT * FROM daily_work_status WHERE appliedShiftIdForDay = :shiftId ORDER BY date DESC")
    suspend fun getStatusByShiftId(shiftId: String): List<DailyWorkStatus>
    
    @Query("SELECT * FROM daily_work_status WHERE appliedShiftIdForDay = :shiftId ORDER BY date DESC")
    fun getStatusByShiftIdFlow(shiftId: String): Flow<List<DailyWorkStatus>>
    
    // Get recent status (last N days)
    @Query("SELECT * FROM daily_work_status WHERE date >= :fromDate ORDER BY date DESC LIMIT :limit")
    suspend fun getRecentStatus(fromDate: LocalDate, limit: Int): List<DailyWorkStatus>
    
    @Query("SELECT * FROM daily_work_status WHERE date >= :fromDate ORDER BY date DESC LIMIT :limit")
    fun getRecentStatusFlow(fromDate: LocalDate, limit: Int): Flow<List<DailyWorkStatus>>
    
    // Statistics queries
    @Query("SELECT COUNT(*) FROM daily_work_status WHERE status = :status AND date BETWEEN :startDate AND :endDate")
    suspend fun getStatusCountForPeriod(status: WorkStatus, startDate: LocalDate, endDate: LocalDate): Int
    
    @Query("SELECT SUM(totalHoursScheduled) FROM daily_work_status WHERE date BETWEEN :startDate AND :endDate")
    suspend fun getTotalHoursForPeriod(startDate: LocalDate, endDate: LocalDate): Double?
    
    @Query("SELECT SUM(otHoursScheduled) FROM daily_work_status WHERE date BETWEEN :startDate AND :endDate")
    suspend fun getTotalOvertimeHoursForPeriod(startDate: LocalDate, endDate: LocalDate): Double?
    
    @Query("SELECT AVG(lateMinutes) FROM daily_work_status WHERE date BETWEEN :startDate AND :endDate AND lateMinutes > 0")
    suspend fun getAverageLateMinutesForPeriod(startDate: LocalDate, endDate: LocalDate): Double?
}
