<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_home" modulePackage="com.workly.app" filePath="app\src\main\res\layout\fragment_home.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.core.widget.NestedScrollView"><Targets><Target tag="layout/fragment_home_0" view="androidx.core.widget.NestedScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="187" endOffset="39"/></Target><Target id="@+id/card_current_status" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="14" startOffset="8" endLine="50" endOffset="59"/></Target><Target id="@+id/text_view_current_status" view="TextView"><Expressions/><location startLine="31" startOffset="16" endLine="37" endOffset="62"/></Target><Target id="@+id/text_view_current_time" view="TextView"><Expressions/><location startLine="39" startOffset="16" endLine="46" endOffset="65"/></Target><Target id="@+id/button_multi_function" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="53" startOffset="8" endLine="63" endOffset="75"/></Target><Target id="@+id/progress_bar_loading" view="ProgressBar"><Expressions/><location startLine="66" startOffset="8" endLine="74" endOffset="74"/></Target><Target id="@+id/card_weather" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="77" startOffset="8" endLine="136" endOffset="59"/></Target><Target id="@+id/text_view_weather_temp" view="TextView"><Expressions/><location startLine="100" startOffset="20" endLine="106" endOffset="43"/></Target><Target id="@+id/text_view_weather_description" view="TextView"><Expressions/><location startLine="108" startOffset="20" endLine="114" endOffset="50"/></Target><Target id="@+id/text_view_weather_location" view="TextView"><Expressions/><location startLine="116" startOffset="20" endLine="122" endOffset="45"/></Target><Target id="@+id/image_view_weather_icon" view="ImageView"><Expressions/><location startLine="126" startOffset="16" endLine="132" endOffset="52"/></Target><Target id="@+id/text_view_notes_title" view="TextView"><Expressions/><location startLine="139" startOffset="8" endLine="148" endOffset="68"/></Target><Target id="@+id/recycler_view_notes" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="150" startOffset="8" endLine="160" endOffset="48"/></Target><Target id="@+id/text_view_attendance_title" view="TextView"><Expressions/><location startLine="163" startOffset="8" endLine="172" endOffset="75"/></Target><Target id="@+id/recycler_view_attendance_history" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="174" startOffset="8" endLine="183" endOffset="58"/></Target></Targets></Layout>