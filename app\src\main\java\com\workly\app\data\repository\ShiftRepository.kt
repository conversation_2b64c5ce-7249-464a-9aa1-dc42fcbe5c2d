package com.workly.app.data.repository

import com.workly.app.data.model.Shift
import kotlinx.coroutines.flow.Flow

interface ShiftRepository {
    fun getAllShifts(): Flow<List<Shift>>
    suspend fun getShiftById(id: String): Shift?
    fun getShiftByIdFlow(id: String): Flow<Shift?>
    suspend fun insertShift(shift: Shift)
    suspend fun insertShifts(shifts: List<Shift>)
    suspend fun updateShift(shift: Shift)
    suspend fun deleteShift(shift: Shift)
    suspend fun deleteShiftById(id: String)
    suspend fun deleteAllShifts()
    suspend fun getShiftsForDay(dayOfWeek: String): List<Shift>
    fun getActiveShifts(): Flow<List<Shift>>
    fun searchShifts(query: String): Flow<List<Shift>>
    suspend fun getShiftsCount(): Int
}
