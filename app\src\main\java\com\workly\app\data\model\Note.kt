package com.workly.app.data.model

import android.os.Parcelable
import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.parcelize.Parcelize
import java.time.LocalDateTime

@Parcelize
@Entity(tableName = "notes")
data class Note(
    @PrimaryKey
    val id: String,
    val title: String,
    val content: String,
    val isPriority: Boolean = false,
    val reminderDateTime: LocalDateTime? = null,
    val associatedShiftIds: List<String> = emptyList(),
    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime,
    val isHiddenFromHome: Boolean = false, // Temporarily hidden from home screen
    val snoozeUntil: LocalDateTime? = null, // Snoozed until this time
    val enableNotifications: Boolean = true // Cho phép hiển thị thông báo/nhắc nhở
) : Parcelable

// Extension functions
fun Note.isActive(): Boolean {
    val now = LocalDateTime.now()
    return !isHiddenFromHome && (snoozeUntil == null || snoozeUntil.isBefore(now))
}

fun Note.hasReminder(): Boolean {
    return reminderDateTime != null && enableNotifications
}

fun Note.isOverdue(): Boolean {
    return reminderDateTime?.let { it.isBefore(LocalDateTime.now()) } ?: false
}

fun Note.getDisplayTitle(): String {
    return if (isPriority) "⭐ $title" else title
}
