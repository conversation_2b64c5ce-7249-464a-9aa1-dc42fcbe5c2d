package com.workly.app.data.repository

import com.workly.app.data.database.dao.NoteDao
import com.workly.app.data.model.Note
import kotlinx.coroutines.flow.Flow
import java.time.LocalDateTime
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class NoteRepositoryImpl @Inject constructor(
    private val noteDao: NoteDao
) : NoteRepository {

    override fun getAllNotes(): Flow<List<Note>> {
        return noteDao.getAllNotes()
    }

    override suspend fun getNoteById(id: String): Note? {
        return noteDao.getNoteById(id)
    }

    override fun getNoteByIdFlow(id: String): Flow<Note?> {
        return noteDao.getNoteByIdFlow(id)
    }

    override suspend fun insertNote(note: Note) {
        noteDao.insertNote(note)
    }

    override suspend fun insertNotes(notes: List<Note>) {
        noteDao.insertNotes(notes)
    }

    override suspend fun updateNote(note: Note) {
        noteDao.updateNote(note)
    }

    override suspend fun deleteNote(note: Note) {
        noteDao.deleteNote(note)
    }

    override suspend fun deleteNoteById(id: String) {
        noteDao.deleteNoteById(id)
    }

    override suspend fun deleteAllNotes() {
        noteDao.deleteAllNotes()
    }

    override suspend fun getActiveNotes(currentTime: LocalDateTime): List<Note> {
        return noteDao.getActiveNotes(currentTime)
    }

    override fun getActiveNotesFlow(currentTime: LocalDateTime): Flow<List<Note>> {
        return noteDao.getActiveNotesFlow(currentTime)
    }

    override suspend fun getPriorityNotes(): List<Note> {
        return noteDao.getPriorityNotes()
    }

    override fun getPriorityNotesFlow(): Flow<List<Note>> {
        return noteDao.getPriorityNotesFlow()
    }

    override suspend fun getNotesWithReminders(): List<Note> {
        return noteDao.getNotesWithReminders()
    }

    override fun getNotesWithRemindersFlow(): Flow<List<Note>> {
        return noteDao.getNotesWithRemindersFlow()
    }

    override suspend fun getOverdueReminders(currentTime: LocalDateTime): List<Note> {
        return noteDao.getOverdueReminders(currentTime)
    }

    override suspend fun getNotesForShift(shiftId: String): List<Note> {
        return noteDao.getNotesForShift(shiftId)
    }

    override fun getNotesForShiftFlow(shiftId: String): Flow<List<Note>> {
        return noteDao.getNotesForShiftFlow(shiftId)
    }

    override suspend fun searchNotes(query: String): List<Note> {
        return noteDao.searchNotes(query)
    }

    override fun searchNotesFlow(query: String): Flow<List<Note>> {
        return noteDao.searchNotesFlow(query)
    }

    override suspend fun getNotesCount(): Int {
        return noteDao.getNotesCount()
    }

    override suspend fun getActiveNotesCount(currentTime: LocalDateTime): Int {
        return noteDao.getActiveNotesCount(currentTime)
    }

    override suspend fun getPriorityNotesCount(): Int {
        return noteDao.getPriorityNotesCount()
    }
}
