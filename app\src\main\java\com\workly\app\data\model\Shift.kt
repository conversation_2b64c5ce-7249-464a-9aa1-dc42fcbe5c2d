package com.workly.app.data.model

import android.os.Parcelable
import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.parcelize.Parcelize
import java.time.LocalDateTime

@Parcelize
@Entity(tableName = "shifts")
data class Shift(
    @PrimaryKey
    val id: String,
    val name: String,
    val startTime: String, // HH:MM format
    val endTime: String, // HH:MM format
    val officeEndTime: String, // HH:MM format
    val departureTime: String, // HH:MM format
    val daysApplied: List<String>, // ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
    val remindBeforeStart: Int, // Minutes before start time to remind
    val remindAfterEnd: Int, // Minutes after end time to remind
    val showPunch: Boolean,
    val breakMinutes: Int,
    val isNightShift: Boolean,
    val workDays: List<Int>, // 0-6 (Sunday-Saturday) - kept for backward compatibility
    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime
) : Parcelable

// Extension functions for day operations
fun Shift.isWorkDay(dayOfWeek: String): Boolean {
    return daysApplied.contains(dayOfWeek)
}

fun Shift.getWorkDaysDisplay(): String {
    return when {
        daysApplied.size == 7 -> "Tất cả các ngày"
        daysApplied.containsAll(listOf("Mon", "Tue", "Wed", "Thu", "Fri")) && 
        !daysApplied.contains("Sat") && !daysApplied.contains("Sun") -> "Thứ 2 - Thứ 6"
        else -> daysApplied.joinToString(", ") { day ->
            when (day) {
                "Mon" -> "T2"
                "Tue" -> "T3"
                "Wed" -> "T4"
                "Thu" -> "T5"
                "Fri" -> "T6"
                "Sat" -> "T7"
                "Sun" -> "CN"
                else -> day
            }
        }
    }
}
