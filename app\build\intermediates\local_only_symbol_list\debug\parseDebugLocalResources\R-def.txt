R_DEF: Internal format may change without notice
local
color background_dark
color background_light
color black
color blue_100
color blue_200
color blue_300
color blue_400
color blue_50
color blue_500
color blue_600
color blue_700
color blue_800
color blue_900
color gray_100
color gray_200
color gray_300
color gray_400
color gray_50
color gray_500
color gray_600
color gray_700
color gray_800
color gray_900
color green_500
color orange_500
color red_500
color surface_dark
color surface_light
color white
color yellow_500
drawable bg_chip_outline
drawable ic_alarm_16
drawable ic_analytics_24
drawable ic_home_24
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_location_on_16
drawable ic_note_16
drawable ic_note_24
drawable ic_notification
drawable ic_schedule_16
drawable ic_schedule_24
drawable ic_settings_24
drawable ic_sunny
id button_multi_function
id button_snooze
id card_current_status
id card_weather
id container
id image_view_weather_icon
id mobile_navigation
id nav_host_fragment_activity_main
id nav_view
id navigation_home
id navigation_notes
id navigation_settings
id navigation_shifts
id navigation_statistics
id progress_bar_loading
id recycler_view_attendance_history
id recycler_view_notes
id text_notes
id text_settings
id text_shifts
id text_statistics
id text_view_attendance_title
id text_view_auto_generated
id text_view_content
id text_view_current_status
id text_view_current_time
id text_view_date
id text_view_location
id text_view_note
id text_view_notes_title
id text_view_reminder
id text_view_shift_count
id text_view_time
id text_view_title
id text_view_type
id text_view_weather_description
id text_view_weather_location
id text_view_weather_temp
id view_priority_indicator
id view_type_indicator
layout activity_main
layout fragment_home
layout fragment_notes
layout fragment_settings
layout fragment_shifts
layout fragment_statistics
layout item_attendance_log
layout item_note
menu bottom_nav_menu
mipmap ic_launcher
mipmap ic_launcher_round
navigation mobile_navigation
string add
string app_name
string attendance_check_in
string attendance_check_out
string attendance_complete
string attendance_go_work
string attendance_history_title
string attendance_punch
string cancel
string check_in
string check_out
string completed
string delete
string edit
string error
string go_work
string loading
string notes_title
string notification_location_service_content
string notification_location_service_title
string notification_note_reminder_title
string notification_shift_reminder_title
string ok
string permission_location_message
string permission_location_title
string permission_notification_message
string permission_notification_title
string ready_to_work
string retry
string save
string status_absent
string status_completed
string status_day_off
string status_early
string status_late
string status_pending
string title_home
string title_notes
string title_settings
string title_shifts
string title_statistics
string weather_cloudy
string weather_rainy
string weather_stormy
string weather_sunny
style Theme.Workly
style Theme.Workly.AppBarOverlay
style Theme.Workly.NoActionBar
style Theme.Workly.PopupOverlay
xml backup_rules
xml data_extraction_rules
