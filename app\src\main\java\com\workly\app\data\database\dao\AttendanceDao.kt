package com.workly.app.data.database.dao

import androidx.room.*
import com.workly.app.data.model.AttendanceLog
import com.workly.app.data.model.AttendanceType
import kotlinx.coroutines.flow.Flow
import java.time.LocalDate
import java.time.LocalDateTime

@Dao
interface AttendanceDao {
    
    @Query("SELECT * FROM attendance_logs ORDER BY time DESC")
    fun getAllAttendanceLogs(): Flow<List<AttendanceLog>>
    
    @Query("SELECT * FROM attendance_logs WHERE id = :id")
    suspend fun getAttendanceLogById(id: String): AttendanceLog?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAttendanceLog(log: AttendanceLog)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAttendanceLogs(logs: List<AttendanceLog>)
    
    @Update
    suspend fun updateAttendanceLog(log: AttendanceLog)
    
    @Delete
    suspend fun deleteAttendanceLog(log: AttendanceLog)
    
    @Query("DELETE FROM attendance_logs WHERE id = :id")
    suspend fun deleteAttendanceLogById(id: String)
    
    @Query("DELETE FROM attendance_logs")
    suspend fun deleteAllAttendanceLogs()
    
    // Get logs for a specific date
    @Query("SELECT * FROM attendance_logs WHERE DATE(time) = :date ORDER BY time ASC")
    suspend fun getLogsForDate(date: LocalDate): List<AttendanceLog>
    
    @Query("SELECT * FROM attendance_logs WHERE DATE(time) = :date ORDER BY time ASC")
    fun getLogsForDateFlow(date: LocalDate): Flow<List<AttendanceLog>>
    
    // Get logs for a date range
    @Query("SELECT * FROM attendance_logs WHERE DATE(time) BETWEEN :startDate AND :endDate ORDER BY time DESC")
    suspend fun getLogsForDateRange(startDate: LocalDate, endDate: LocalDate): List<AttendanceLog>
    
    @Query("SELECT * FROM attendance_logs WHERE DATE(time) BETWEEN :startDate AND :endDate ORDER BY time DESC")
    fun getLogsForDateRangeFlow(startDate: LocalDate, endDate: LocalDate): Flow<List<AttendanceLog>>
    
    // Get logs by shift ID
    @Query("SELECT * FROM attendance_logs WHERE shiftId = :shiftId ORDER BY time DESC")
    suspend fun getLogsByShiftId(shiftId: String): List<AttendanceLog>
    
    @Query("SELECT * FROM attendance_logs WHERE shiftId = :shiftId ORDER BY time DESC")
    fun getLogsByShiftIdFlow(shiftId: String): Flow<List<AttendanceLog>>
    
    // Get logs by type
    @Query("SELECT * FROM attendance_logs WHERE type = :type ORDER BY time DESC")
    suspend fun getLogsByType(type: AttendanceType): List<AttendanceLog>
    
    // Get recent logs (last N days)
    @Query("SELECT * FROM attendance_logs WHERE time >= :fromDate ORDER BY time DESC LIMIT :limit")
    suspend fun getRecentLogs(fromDate: LocalDateTime, limit: Int): List<AttendanceLog>
    
    @Query("SELECT * FROM attendance_logs WHERE time >= :fromDate ORDER BY time DESC LIMIT :limit")
    fun getRecentLogsFlow(fromDate: LocalDateTime, limit: Int): Flow<List<AttendanceLog>>
    
    // Get auto-generated logs
    @Query("SELECT * FROM attendance_logs WHERE isAutoGenerated = 1 ORDER BY time DESC")
    suspend fun getAutoGeneratedLogs(): List<AttendanceLog>
    
    // Get manual logs
    @Query("SELECT * FROM attendance_logs WHERE isAutoGenerated = 0 ORDER BY time DESC")
    suspend fun getManualLogs(): List<AttendanceLog>
    
    // Get logs count
    @Query("SELECT COUNT(*) FROM attendance_logs")
    suspend fun getLogsCount(): Int
    
    // Get logs count for date
    @Query("SELECT COUNT(*) FROM attendance_logs WHERE DATE(time) = :date")
    suspend fun getLogsCountForDate(date: LocalDate): Int
}
