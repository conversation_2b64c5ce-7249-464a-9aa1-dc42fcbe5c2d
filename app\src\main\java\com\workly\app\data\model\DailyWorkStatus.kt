package com.workly.app.data.model

import android.os.Parcelable
import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.parcelize.Parcelize
import java.time.LocalDate
import java.time.LocalDateTime

@Parcelize
@Entity(tableName = "daily_work_status")
data class DailyWorkStatus(
    @PrimaryKey
    val date: LocalDate,
    val status: WorkStatus,
    val appliedShiftIdForDay: String? = null, // ID ca làm việc áp dụng cho ngày này
    val vaoLogTime: LocalDateTime? = null, // Check-in time
    val raLogTime: LocalDateTime? = null, // Check-out time
    val standardHoursScheduled: Double = 0.0,
    val otHoursScheduled: Double = 0.0,
    val sundayHoursScheduled: Double = 0.0,
    val nightHoursScheduled: Double = 0.0,
    val totalHoursScheduled: Double = 0.0,
    val lateMinutes: Int = 0,
    val earlyMinutes: Int = 0,
    val isHolidayWork: Boolean = false,
    val isManualOverride: Boolean = false // Đ<PERSON>h dấu trạng thái đ<PERSON> cập nhật thủ công
) : Parcelable

enum class WorkStatus {
    COMPLETED,
    LATE,
    EARLY,
    ABSENT,
    MANUAL_PRESENT,
    MANUAL_ABSENT,
    MANUAL_HOLIDAY,
    MANUAL_COMPLETED,
    MANUAL_REVIEW,
    DAY_OFF,
    PENDING,
    NGHI_PHEP,
    NGHI_BENH,
    NGHI_LE,
    VANG_MAT,
    CONG_TAC,
    DU_CONG,
    RV,
    DI_MUON,
    VE_SOM,
    DI_MUON_VE_SOM,
    CHUA_DI,
    DA_DI_CHUA_VAO,
    CHUA_RA,
    TINH_THEO_CHAM_CONG,
    THIEU_LOG,
    XOA_TRANG_THAI_THU_CONG
}

// Extension functions
fun WorkStatus.getDisplayText(): String {
    return when (this) {
        WorkStatus.COMPLETED -> "Hoàn thành"
        WorkStatus.LATE -> "Đi muộn"
        WorkStatus.EARLY -> "Về sớm"
        WorkStatus.ABSENT -> "Vắng mặt"
        WorkStatus.MANUAL_PRESENT -> "Có mặt (thủ công)"
        WorkStatus.MANUAL_ABSENT -> "Vắng mặt (thủ công)"
        WorkStatus.MANUAL_HOLIDAY -> "Nghỉ lễ (thủ công)"
        WorkStatus.MANUAL_COMPLETED -> "Hoàn thành (thủ công)"
        WorkStatus.MANUAL_REVIEW -> "Cần xem xét (thủ công)"
        WorkStatus.DAY_OFF -> "Nghỉ"
        WorkStatus.PENDING -> "Chờ xử lý"
        WorkStatus.NGHI_PHEP -> "Nghỉ phép"
        WorkStatus.NGHI_BENH -> "Nghỉ bệnh"
        WorkStatus.NGHI_LE -> "Nghỉ lễ"
        WorkStatus.VANG_MAT -> "Vắng mặt"
        WorkStatus.CONG_TAC -> "Công tác"
        WorkStatus.DU_CONG -> "Đủ công"
        WorkStatus.RV -> "Ra về"
        WorkStatus.DI_MUON -> "Đi muộn"
        WorkStatus.VE_SOM -> "Về sớm"
        WorkStatus.DI_MUON_VE_SOM -> "Đi muộn, về sớm"
        WorkStatus.CHUA_DI -> "Chưa đi"
        WorkStatus.DA_DI_CHUA_VAO -> "Đã đi, chưa vào"
        WorkStatus.CHUA_RA -> "Chưa ra"
        WorkStatus.TINH_THEO_CHAM_CONG -> "Tính theo chấm công"
        WorkStatus.THIEU_LOG -> "Thiếu log"
        WorkStatus.XOA_TRANG_THAI_THU_CONG -> "Xóa trạng thái thủ công"
    }
}

fun WorkStatus.getColor(): Int {
    return when (this) {
        WorkStatus.COMPLETED, WorkStatus.DU_CONG -> android.graphics.Color.GREEN
        WorkStatus.LATE, WorkStatus.DI_MUON, WorkStatus.DI_MUON_VE_SOM -> android.graphics.Color.RED
        WorkStatus.EARLY, WorkStatus.VE_SOM -> android.graphics.Color.YELLOW
        WorkStatus.ABSENT, WorkStatus.VANG_MAT, WorkStatus.CHUA_DI -> android.graphics.Color.GRAY
        WorkStatus.DAY_OFF, WorkStatus.NGHI_PHEP, WorkStatus.NGHI_BENH, WorkStatus.NGHI_LE -> android.graphics.Color.BLUE
        WorkStatus.PENDING, WorkStatus.CHUA_RA, WorkStatus.DA_DI_CHUA_VAO -> android.graphics.Color.CYAN
        else -> android.graphics.Color.BLACK
    }
}
