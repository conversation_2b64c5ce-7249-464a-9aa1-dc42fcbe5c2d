package com.workly.app.data.database.dao

import androidx.room.*
import com.workly.app.data.model.Shift
import kotlinx.coroutines.flow.Flow

@Dao
interface ShiftDao {
    
    @Query("SELECT * FROM shifts ORDER BY createdAt DESC")
    fun getAllShifts(): Flow<List<Shift>>
    
    @Query("SELECT * FROM shifts WHERE id = :id")
    suspend fun getShiftById(id: String): Shift?
    
    @Query("SELECT * FROM shifts WHERE id = :id")
    fun getShiftByIdFlow(id: String): Flow<Shift?>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertShift(shift: Shift)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertShifts(shifts: List<Shift>)
    
    @Update
    suspend fun updateShift(shift: Shift)
    
    @Delete
    suspend fun deleteShift(shift: Shift)
    
    @Query("DELETE FROM shifts WHERE id = :id")
    suspend fun deleteShiftById(id: String)
    
    @Query("DELETE FROM shifts")
    suspend fun deleteAllShifts()
    
    // Get shifts that apply to a specific day
    @Query("SELECT * FROM shifts WHERE daysApplied LIKE '%' || :dayOfWeek || '%'")
    suspend fun getShiftsForDay(dayOfWeek: String): List<Shift>
    
    // Get active shifts (not deleted)
    @Query("SELECT * FROM shifts ORDER BY name ASC")
    fun getActiveShifts(): Flow<List<Shift>>
    
    // Search shifts by name
    @Query("SELECT * FROM shifts WHERE name LIKE '%' || :query || '%' ORDER BY name ASC")
    fun searchShifts(query: String): Flow<List<Shift>>
    
    // Get shifts count
    @Query("SELECT COUNT(*) FROM shifts")
    suspend fun getShiftsCount(): Int
}
