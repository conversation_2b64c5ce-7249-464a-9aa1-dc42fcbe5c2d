#Thu Jul 03 05:43:30 ICT 2025
com.workly.app-main-5\:/drawable/bg_chip_outline.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\bg_chip_outline.xml
com.workly.app-main-5\:/drawable/ic_alarm_16.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_alarm_16.xml
com.workly.app-main-5\:/drawable/ic_analytics_24.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_analytics_24.xml
com.workly.app-main-5\:/drawable/ic_home_24.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_home_24.xml
com.workly.app-main-5\:/drawable/ic_launcher_background.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_launcher_background.xml
com.workly.app-main-5\:/drawable/ic_launcher_foreground.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_launcher_foreground.xml
com.workly.app-main-5\:/drawable/ic_location_on_16.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_location_on_16.xml
com.workly.app-main-5\:/drawable/ic_note_16.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_note_16.xml
com.workly.app-main-5\:/drawable/ic_note_24.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_note_24.xml
com.workly.app-main-5\:/drawable/ic_notification.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_notification.xml
com.workly.app-main-5\:/drawable/ic_schedule_16.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_schedule_16.xml
com.workly.app-main-5\:/drawable/ic_schedule_24.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_schedule_24.xml
com.workly.app-main-5\:/drawable/ic_settings_24.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_settings_24.xml
com.workly.app-main-5\:/drawable/ic_sunny.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_sunny.xml
com.workly.app-main-5\:/menu/bottom_nav_menu.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\menu\\bottom_nav_menu.xml
com.workly.app-main-5\:/mipmap-anydpi-v26/ic_launcher.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v26\\ic_launcher.xml
com.workly.app-main-5\:/mipmap-anydpi-v26/ic_launcher_round.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v26\\ic_launcher_round.xml
com.workly.app-main-5\:/mipmap-hdpi/ic_launcher.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher.xml
com.workly.app-main-5\:/mipmap-hdpi/ic_launcher_round.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher_round.xml
com.workly.app-main-5\:/mipmap-mdpi/ic_launcher.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher.xml
com.workly.app-main-5\:/mipmap-mdpi/ic_launcher_round.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher_round.xml
com.workly.app-main-5\:/mipmap-xhdpi/ic_launcher.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher.xml
com.workly.app-main-5\:/mipmap-xhdpi/ic_launcher_round.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher_round.xml
com.workly.app-main-5\:/mipmap-xxhdpi/ic_launcher.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher.xml
com.workly.app-main-5\:/mipmap-xxhdpi/ic_launcher_round.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher_round.xml
com.workly.app-main-5\:/mipmap-xxxhdpi/ic_launcher.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher.xml
com.workly.app-main-5\:/mipmap-xxxhdpi/ic_launcher_round.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher_round.xml
com.workly.app-main-5\:/navigation/mobile_navigation.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\navigation\\mobile_navigation.xml
com.workly.app-main-5\:/xml/backup_rules.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\backup_rules.xml
com.workly.app-main-5\:/xml/data_extraction_rules.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\data_extraction_rules.xml
com.workly.app-packageDebugResources-2\:/layout/activity_main.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_main.xml
com.workly.app-packageDebugResources-2\:/layout/fragment_home.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_home.xml
com.workly.app-packageDebugResources-2\:/layout/fragment_notes.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_notes.xml
com.workly.app-packageDebugResources-2\:/layout/fragment_settings.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_settings.xml
com.workly.app-packageDebugResources-2\:/layout/fragment_shifts.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_shifts.xml
com.workly.app-packageDebugResources-2\:/layout/fragment_statistics.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_statistics.xml
com.workly.app-packageDebugResources-2\:/layout/item_attendance_log.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_attendance_log.xml
com.workly.app-packageDebugResources-2\:/layout/item_note.xml=G\:\\IT\\PROJECT-IDEAL\\workly_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_note.xml
