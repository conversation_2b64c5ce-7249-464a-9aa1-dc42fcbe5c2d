1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.workly.app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- Permissions -->
12    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
12-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:6:5-79
12-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
13-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:7:5-81
13-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:7:22-78
14    <uses-permission android:name="android.permission.VIBRATE" />
14-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:8:5-66
14-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:8:22-63
15    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
15-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:9:5-81
15-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:9:22-78
16    <uses-permission android:name="android.permission.WAKE_LOCK" />
16-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:10:5-68
16-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:10:22-65
17    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
17-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:11:5-77
17-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:11:22-74
18    <uses-permission android:name="android.permission.INTERNET" />
18-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:12:5-67
18-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:12:22-64
19    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
19-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:13:5-79
19-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:13:22-76
20    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
20-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:14:5-77
20-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:14:22-74
21    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION" />
21-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:15:5-86
21-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:15:22-83
22
23    <uses-feature
23-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc77bc2feb0e2b4b8145e3e0f896677f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
24        android:glEsVersion="0x00020000"
24-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc77bc2feb0e2b4b8145e3e0f896677f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
25        android:required="true" />
25-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc77bc2feb0e2b4b8145e3e0f896677f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
26
27    <queries>
27-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc77bc2feb0e2b4b8145e3e0f896677f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
28
29        <!-- Needs to be explicitly declared on Android R+ -->
30        <package android:name="com.google.android.apps.maps" />
30-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc77bc2feb0e2b4b8145e3e0f896677f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
30-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc77bc2feb0e2b4b8145e3e0f896677f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
31    </queries>
32
33    <permission
33-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8af33194f3dfc2f8bc08d7753e6a12a4\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
34        android:name="com.workly.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
34-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8af33194f3dfc2f8bc08d7753e6a12a4\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
35        android:protectionLevel="signature" />
35-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8af33194f3dfc2f8bc08d7753e6a12a4\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
36
37    <uses-permission android:name="com.workly.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
37-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8af33194f3dfc2f8bc08d7753e6a12a4\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
37-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8af33194f3dfc2f8bc08d7753e6a12a4\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
38
39    <application
39-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:17:5-77:19
40        android:name="com.workly.app.WorklyApplication"
40-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:18:9-42
41        android:allowBackup="true"
41-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:19:9-35
42        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
42-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8af33194f3dfc2f8bc08d7753e6a12a4\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
43        android:dataExtractionRules="@xml/data_extraction_rules"
43-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:20:9-65
44        android:debuggable="true"
45        android:extractNativeLibs="false"
46        android:fullBackupContent="@xml/backup_rules"
46-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:21:9-54
47        android:icon="@mipmap/ic_launcher"
47-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:22:9-43
48        android:label="@string/app_name"
48-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:23:9-41
49        android:roundIcon="@mipmap/ic_launcher_round"
49-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:24:9-54
50        android:supportsRtl="true"
50-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:25:9-35
51        android:testOnly="true"
52        android:theme="@style/Theme.Workly" >
52-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:26:9-44
53
54        <!-- Main Activity -->
55        <activity
55-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:30:9-38:20
56            android:name="com.workly.app.ui.MainActivity"
56-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:31:13-44
57            android:exported="true"
57-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:32:13-36
58            android:theme="@style/Theme.Workly.NoActionBar" >
58-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:33:13-60
59            <intent-filter>
59-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:34:13-37:29
60                <action android:name="android.intent.action.MAIN" />
60-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:35:17-69
60-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:35:25-66
61
62                <category android:name="android.intent.category.LAUNCHER" />
62-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:36:17-77
62-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:36:27-74
63            </intent-filter>
64        </activity>
65
66        <!-- Boot Receiver for alarms -->
67        <receiver
67-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:41:9-51:20
68            android:name="com.workly.app.receivers.BootReceiver"
68-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:42:13-51
69            android:enabled="true"
69-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:43:13-35
70            android:exported="true" >
70-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:44:13-36
71            <intent-filter android:priority="1000" >
71-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:45:13-50:29
71-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:45:28-51
72                <action android:name="android.intent.action.BOOT_COMPLETED" />
72-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:46:17-79
72-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:46:25-76
73                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
73-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:47:17-84
73-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:47:25-81
74                <action android:name="android.intent.action.PACKAGE_REPLACED" />
74-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:48:17-81
74-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:48:25-78
75
76                <data android:scheme="package" />
76-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:49:17-50
76-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:49:23-47
77            </intent-filter>
78        </receiver>
79
80        <!-- Alarm Receiver -->
81        <receiver
81-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:54:9-57:40
82            android:name="com.workly.app.receivers.AlarmReceiver"
82-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:55:13-52
83            android:enabled="true"
83-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:56:13-35
84            android:exported="false" />
84-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:57:13-37
85
86        <!-- Location Service -->
87        <service
87-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:60:9-64:56
88            android:name="com.workly.app.services.LocationTrackingService"
88-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:61:13-61
89            android:enabled="true"
89-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:62:13-35
90            android:exported="false"
90-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:63:13-37
91            android:foregroundServiceType="location" />
91-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:64:13-53
92
93        <!-- Work Manager -->
94        <provider
95            android:name="androidx.startup.InitializationProvider"
95-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:68:13-67
96            android:authorities="com.workly.app.androidx-startup"
96-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:69:13-68
97            android:exported="false" >
97-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:70:13-37
98            <meta-data
98-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:72:13-74:52
99                android:name="androidx.work.WorkManagerInitializer"
99-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:73:17-68
100                android:value="androidx.startup" />
100-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:74:17-49
101            <meta-data
101-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87b27440a33b3dcf7fa56b0a1a24dfeb\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
102                android:name="androidx.emoji2.text.EmojiCompatInitializer"
102-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87b27440a33b3dcf7fa56b0a1a24dfeb\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
103                android:value="androidx.startup" />
103-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87b27440a33b3dcf7fa56b0a1a24dfeb\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
104            <meta-data
104-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4a3ab3a1a836618d2322c30118e7a126\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
105                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
105-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4a3ab3a1a836618d2322c30118e7a126\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
106                android:value="androidx.startup" />
106-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4a3ab3a1a836618d2322c30118e7a126\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
107            <meta-data
107-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
108                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
108-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
109                android:value="androidx.startup" />
109-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
110        </provider>
111
112        <!-- Needs to be explicitly declared on P+ -->
113        <uses-library
113-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc77bc2feb0e2b4b8145e3e0f896677f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
114            android:name="org.apache.http.legacy"
114-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc77bc2feb0e2b4b8145e3e0f896677f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
115            android:required="false" />
115-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc77bc2feb0e2b4b8145e3e0f896677f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
116
117        <activity
117-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3678371e4b9f291b0b6ff6bca1ac707a\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
118            android:name="com.google.android.gms.common.api.GoogleApiActivity"
118-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3678371e4b9f291b0b6ff6bca1ac707a\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
119            android:exported="false"
119-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3678371e4b9f291b0b6ff6bca1ac707a\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
120            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
120-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3678371e4b9f291b0b6ff6bca1ac707a\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
121
122        <meta-data
122-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\240c634c8d2ef38e2610db507fc6f6e4\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
123            android:name="com.google.android.gms.version"
123-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\240c634c8d2ef38e2610db507fc6f6e4\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
124            android:value="@integer/google_play_services_version" />
124-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\240c634c8d2ef38e2610db507fc6f6e4\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
125
126        <service
126-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
127            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
127-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
128            android:directBootAware="false"
128-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
129            android:enabled="@bool/enable_system_alarm_service_default"
129-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
130            android:exported="false" />
130-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
131        <service
131-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
132            android:name="androidx.work.impl.background.systemjob.SystemJobService"
132-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
133            android:directBootAware="false"
133-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
134            android:enabled="@bool/enable_system_job_service_default"
134-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
135            android:exported="true"
135-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
136            android:permission="android.permission.BIND_JOB_SERVICE" />
136-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
137        <service
137-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
138            android:name="androidx.work.impl.foreground.SystemForegroundService"
138-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
139            android:directBootAware="false"
139-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
140            android:enabled="@bool/enable_system_foreground_service_default"
140-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
141            android:exported="false" />
141-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
142
143        <receiver
143-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
144            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
144-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
145            android:directBootAware="false"
145-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
146            android:enabled="true"
146-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
147            android:exported="false" />
147-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
148        <receiver
148-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
149            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
149-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
150            android:directBootAware="false"
150-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
151            android:enabled="false"
151-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
152            android:exported="false" >
152-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
153            <intent-filter>
153-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
154                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
154-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
154-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
155                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
155-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
155-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
156            </intent-filter>
157        </receiver>
158        <receiver
158-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
159            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
159-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
160            android:directBootAware="false"
160-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
161            android:enabled="false"
161-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
162            android:exported="false" >
162-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
163            <intent-filter>
163-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
164                <action android:name="android.intent.action.BATTERY_OKAY" />
164-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
164-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
165                <action android:name="android.intent.action.BATTERY_LOW" />
165-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
165-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
166            </intent-filter>
167        </receiver>
168        <receiver
168-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
169            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
169-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
170            android:directBootAware="false"
170-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
171            android:enabled="false"
171-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
172            android:exported="false" >
172-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
173            <intent-filter>
173-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
174                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
174-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
174-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
175                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
175-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
175-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
176            </intent-filter>
177        </receiver>
178        <receiver
178-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
179            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
179-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
180            android:directBootAware="false"
180-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
181            android:enabled="false"
181-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
182            android:exported="false" >
182-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
183            <intent-filter>
183-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
184                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
184-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
184-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
185            </intent-filter>
186        </receiver>
187        <receiver
187-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
188            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
188-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
189            android:directBootAware="false"
189-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
190            android:enabled="false"
190-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
191            android:exported="false" >
191-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
192            <intent-filter>
192-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
193                <action android:name="android.intent.action.BOOT_COMPLETED" />
193-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:46:17-79
193-->G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:46:25-76
194                <action android:name="android.intent.action.TIME_SET" />
194-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
194-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
195                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
195-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
195-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
196            </intent-filter>
197        </receiver>
198        <receiver
198-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
199            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
199-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
200            android:directBootAware="false"
200-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
201            android:enabled="@bool/enable_system_alarm_service_default"
201-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
202            android:exported="false" >
202-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
203            <intent-filter>
203-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
204                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
204-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
204-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
205            </intent-filter>
206        </receiver>
207        <receiver
207-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
208            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
208-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
209            android:directBootAware="false"
209-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
210            android:enabled="true"
210-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
211            android:exported="true"
211-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
212            android:permission="android.permission.DUMP" >
212-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
213            <intent-filter>
213-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
214                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
214-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
214-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
215            </intent-filter>
216        </receiver>
217
218        <uses-library
218-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84ec0ce9a05b1e6837aff9f99a1dfa92\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
219            android:name="androidx.window.extensions"
219-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84ec0ce9a05b1e6837aff9f99a1dfa92\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
220            android:required="false" />
220-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84ec0ce9a05b1e6837aff9f99a1dfa92\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
221        <uses-library
221-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84ec0ce9a05b1e6837aff9f99a1dfa92\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
222            android:name="androidx.window.sidecar"
222-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84ec0ce9a05b1e6837aff9f99a1dfa92\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
223            android:required="false" />
223-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84ec0ce9a05b1e6837aff9f99a1dfa92\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
224
225        <service
225-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb3e658c23c1e3a637ad69981f7e53a5\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
226            android:name="androidx.room.MultiInstanceInvalidationService"
226-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb3e658c23c1e3a637ad69981f7e53a5\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
227            android:directBootAware="true"
227-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb3e658c23c1e3a637ad69981f7e53a5\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
228            android:exported="false" />
228-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb3e658c23c1e3a637ad69981f7e53a5\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
229
230        <receiver
230-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
231            android:name="androidx.profileinstaller.ProfileInstallReceiver"
231-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
232            android:directBootAware="false"
232-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
233            android:enabled="true"
233-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
234            android:exported="true"
234-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
235            android:permission="android.permission.DUMP" >
235-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
236            <intent-filter>
236-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
237                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
237-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
237-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
238            </intent-filter>
239            <intent-filter>
239-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
240                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
240-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
240-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
241            </intent-filter>
242            <intent-filter>
242-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
243                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
243-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
243-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
244            </intent-filter>
245            <intent-filter>
245-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
246                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
246-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
246-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
247            </intent-filter>
248        </receiver>
249    </application>
250
251</manifest>
