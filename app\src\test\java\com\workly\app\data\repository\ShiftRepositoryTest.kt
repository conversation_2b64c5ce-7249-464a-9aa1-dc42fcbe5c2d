package com.workly.app.data.repository

import com.workly.app.data.database.dao.ShiftDao
import com.workly.app.data.model.Shift
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.time.LocalDateTime
import kotlin.test.assertEquals

class ShiftRepositoryTest {

    @Mock
    private lateinit var shiftDao: ShiftDao

    private lateinit var repository: ShiftRepositoryImpl

    @Before
    fun setup() {
        MockitoAnnotations.openMocks(this)
        repository = ShiftRepositoryImpl(shiftDao)
    }

    @Test
    fun `getAllShifts should return flow from dao`() = runTest {
        // Given
        val shifts = listOf(createTestShift())
        whenever(shiftDao.getAllShifts()).thenReturn(flowOf(shifts))

        // When
        val result = repository.getAllShifts()

        // Then
        verify(shiftDao).getAllShifts()
        // Additional assertions can be added here
    }

    @Test
    fun `insertShift should call dao insertShift`() = runTest {
        // Given
        val shift = createTestShift()

        // When
        repository.insertShift(shift)

        // Then
        verify(shiftDao).insertShift(shift)
    }

    @Test
    fun `getShiftById should return shift from dao`() = runTest {
        // Given
        val shiftId = "test-id"
        val shift = createTestShift()
        whenever(shiftDao.getShiftById(shiftId)).thenReturn(shift)

        // When
        val result = repository.getShiftById(shiftId)

        // Then
        verify(shiftDao).getShiftById(shiftId)
        assertEquals(shift, result)
    }

    private fun createTestShift(): Shift {
        return Shift(
            id = "test-id",
            name = "Test Shift",
            startTime = "09:00",
            endTime = "17:00",
            officeEndTime = "17:30",
            departureTime = "18:00",
            daysApplied = listOf("Mon", "Tue", "Wed", "Thu", "Fri"),
            remindBeforeStart = 30,
            remindAfterEnd = 15,
            showPunch = true,
            breakMinutes = 60,
            isNightShift = false,
            workDays = listOf(1, 2, 3, 4, 5),
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now()
        )
    }
}
