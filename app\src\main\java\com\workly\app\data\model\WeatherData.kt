package com.workly.app.data.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.time.LocalDateTime

@Parcelize
data class WeatherData(
    val current: CurrentWeather,
    val forecast: List<WeatherForecast>,
    val warnings: List<WeatherWarning> = emptyList(),
    val lastUpdated: LocalDateTime
) : Parcelable

@Parcelize
data class CurrentWeather(
    val temperature: Double,
    val description: String,
    val icon: String,
    val location: String
) : Parcelable

@Parcelize
data class WeatherForecast(
    val time: LocalDateTime,
    val temperature: Double,
    val description: String,
    val icon: String
) : Parcelable

@Parcelize
data class WeatherWarning(
    val type: WeatherWarningType,
    val message: String,
    val location: WeatherWarningLocation,
    val time: LocalDateTime
) : Parcelable

enum class WeatherWarningType {
    RAIN, HEAT, COLD, STORM, SNOW
}

enum class WeatherWarningLocation {
    HOME, WORK
}

// Extension functions
fun WeatherWarningType.getDisplayText(): String {
    return when (this) {
        WeatherWarningType.RAIN -> "Mưa"
        WeatherWarningType.HEAT -> "Nắng nóng"
        WeatherWarningType.COLD -> "Lạnh"
        WeatherWarningType.STORM -> "Bão"
        WeatherWarningType.SNOW -> "Tuyết"
    }
}

fun WeatherWarningLocation.getDisplayText(): String {
    return when (this) {
        WeatherWarningLocation.HOME -> "Nhà"
        WeatherWarningLocation.WORK -> "Công ty"
    }
}

fun CurrentWeather.getTemperatureDisplay(): String {
    return "${temperature.toInt()}°C"
}
