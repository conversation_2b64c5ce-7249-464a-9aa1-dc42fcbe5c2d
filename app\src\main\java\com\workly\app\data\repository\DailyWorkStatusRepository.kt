package com.workly.app.data.repository

import com.workly.app.data.model.DailyWorkStatus
import com.workly.app.data.model.WorkStatus
import kotlinx.coroutines.flow.Flow
import java.time.LocalDate

interface DailyWorkStatusRepository {
    fun getAllDailyWorkStatus(): Flow<List<DailyWorkStatus>>
    suspend fun getDailyWorkStatusByDate(date: LocalDate): DailyWorkStatus?
    fun getDailyWorkStatusByDateFlow(date: LocalDate): Flow<DailyWorkStatus?>
    suspend fun insertDailyWorkStatus(status: DailyWorkStatus)
    suspend fun insertDailyWorkStatuses(statuses: List<DailyWorkStatus>)
    suspend fun updateDailyWorkStatus(status: DailyWorkStatus)
    suspend fun deleteDailyWorkStatus(status: DailyWorkStatus)
    suspend fun deleteDailyWorkStatusByDate(date: LocalDate)
    suspend fun deleteAllDailyWorkStatus()
    suspend fun getStatusForDateRange(startDate: LocalDate, endDate: LocalDate): List<DailyWorkStatus>
    fun getStatusForDateRangeFlow(startDate: LocalDate, endDate: LocalDate): Flow<List<DailyWorkStatus>>
    suspend fun getStatusByType(status: WorkStatus): List<DailyWorkStatus>
    fun getStatusByTypeFlow(status: WorkStatus): Flow<List<DailyWorkStatus>>
    suspend fun getManualOverrides(): List<DailyWorkStatus>
    fun getManualOverridesFlow(): Flow<List<DailyWorkStatus>>
    suspend fun getHolidayWork(): List<DailyWorkStatus>
    fun getHolidayWorkFlow(): Flow<List<DailyWorkStatus>>
    suspend fun getStatusByShiftId(shiftId: String): List<DailyWorkStatus>
    fun getStatusByShiftIdFlow(shiftId: String): Flow<List<DailyWorkStatus>>
    suspend fun getRecentStatus(fromDate: LocalDate, limit: Int): List<DailyWorkStatus>
    fun getRecentStatusFlow(fromDate: LocalDate, limit: Int): Flow<List<DailyWorkStatus>>
    suspend fun getStatusCountForPeriod(status: WorkStatus, startDate: LocalDate, endDate: LocalDate): Int
    suspend fun getTotalHoursForPeriod(startDate: LocalDate, endDate: LocalDate): Double?
    suspend fun getTotalOvertimeHoursForPeriod(startDate: LocalDate, endDate: LocalDate): Double?
    suspend fun getAverageLateMinutesForPeriod(startDate: LocalDate, endDate: LocalDate): Double?
}
