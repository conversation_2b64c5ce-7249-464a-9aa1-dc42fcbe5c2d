<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_notes" modulePackage="com.workly.app" filePath="app\src\main\res\layout\fragment_notes.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_notes_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="21" endOffset="51"/></Target><Target id="@+id/text_notes" view="TextView"><Expressions/><location startLine="8" startOffset="4" endLine="20" endOffset="51"/></Target></Targets></Layout>