package com.workly.app.data.repository

import com.workly.app.data.model.Note
import kotlinx.coroutines.flow.Flow
import java.time.LocalDateTime

interface NoteRepository {
    fun getAllNotes(): Flow<List<Note>>
    suspend fun getNoteById(id: String): Note?
    fun getNoteByIdFlow(id: String): Flow<Note?>
    suspend fun insertNote(note: Note)
    suspend fun insertNotes(notes: List<Note>)
    suspend fun updateNote(note: Note)
    suspend fun deleteNote(note: Note)
    suspend fun deleteNoteById(id: String)
    suspend fun deleteAllNotes()
    suspend fun getActiveNotes(currentTime: LocalDateTime = LocalDateTime.now()): List<Note>
    fun getActiveNotesFlow(currentTime: LocalDateTime = LocalDateTime.now()): Flow<List<Note>>
    suspend fun getPriorityNotes(): List<Note>
    fun getPriorityNotesFlow(): Flow<List<Note>>
    suspend fun getNotesWithReminders(): List<Note>
    fun getNotesWithRemindersFlow(): Flow<List<Note>>
    suspend fun getOverdueReminders(currentTime: LocalDateTime = LocalDateTime.now()): List<Note>
    suspend fun getNotesForShift(shiftId: String): List<Note>
    fun getNotesForShiftFlow(shiftId: String): Flow<List<Note>>
    suspend fun searchNotes(query: String): List<Note>
    fun searchNotesFlow(query: String): Flow<List<Note>>
    suspend fun getNotesCount(): Int
    suspend fun getActiveNotesCount(currentTime: LocalDateTime = LocalDateTime.now()): Int
    suspend fun getPriorityNotesCount(): Int
}
