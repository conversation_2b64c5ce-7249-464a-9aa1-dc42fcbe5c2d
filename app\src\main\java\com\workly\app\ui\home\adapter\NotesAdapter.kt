package com.workly.app.ui.home.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.workly.app.data.model.Note
import com.workly.app.data.model.getDisplayTitle
import com.workly.app.data.model.hasReminder
import com.workly.app.data.model.isOverdue
import com.workly.app.databinding.ItemNoteBinding
import java.time.format.DateTimeFormatter

class NotesAdapter(
    private val onNoteClick: (Note) -> Unit,
    private val onNoteSnooze: (Note) -> Unit
) : ListAdapter<Note, NotesAdapter.ViewHolder>(NoteDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemNoteBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class ViewHolder(
        private val binding: ItemNoteBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        init {
            binding.root.setOnClickListener {
                val position = adapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    onNoteClick(getItem(position))
                }
            }
            
            binding.buttonSnooze.setOnClickListener {
                val position = adapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    onNoteSnooze(getItem(position))
                }
            }
        }

        fun bind(note: Note) {
            binding.apply {
                textViewTitle.text = note.getDisplayTitle()
                textViewContent.text = note.content
                
                // Show creation date
                textViewDate.text = note.createdAt.format(
                    DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")
                )
                
                // Show reminder info if available
                if (note.hasReminder()) {
                    note.reminderDateTime?.let { reminderTime ->
                        textViewReminder.text = "Nhắc nhở: ${reminderTime.format(
                            DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")
                        )}"
                        textViewReminder.visibility = android.view.View.VISIBLE
                        
                        // Highlight overdue reminders
                        if (note.isOverdue()) {
                            textViewReminder.setTextColor(
                                android.graphics.Color.RED
                            )
                        } else {
                            textViewReminder.setTextColor(
                                android.graphics.Color.GRAY
                            )
                        }
                    }
                } else {
                    textViewReminder.visibility = android.view.View.GONE
                }
                
                // Show priority indicator
                if (note.isPriority) {
                    viewPriorityIndicator.visibility = android.view.View.VISIBLE
                } else {
                    viewPriorityIndicator.visibility = android.view.View.GONE
                }
                
                // Show snooze button only if note has reminder
                if (note.hasReminder()) {
                    buttonSnooze.visibility = android.view.View.VISIBLE
                } else {
                    buttonSnooze.visibility = android.view.View.GONE
                }
                
                // Show associated shifts count if any
                if (note.associatedShiftIds.isNotEmpty()) {
                    textViewShiftCount.text = "${note.associatedShiftIds.size} ca làm việc"
                    textViewShiftCount.visibility = android.view.View.VISIBLE
                } else {
                    textViewShiftCount.visibility = android.view.View.GONE
                }
            }
        }
    }

    private class NoteDiffCallback : DiffUtil.ItemCallback<Note>() {
        override fun areItemsTheSame(oldItem: Note, newItem: Note): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: Note, newItem: Note): Boolean {
            return oldItem == newItem
        }
    }
}
