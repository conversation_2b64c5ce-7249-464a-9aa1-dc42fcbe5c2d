package com.workly.app.services

import android.content.Context
import com.workly.app.data.model.*
import com.workly.app.data.repository.SettingsRepository
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.first
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import retrofit2.http.GET
import retrofit2.http.Query
import java.time.LocalDateTime
import javax.inject.Inject
import javax.inject.Singleton

// Weather API interface (using OpenWeatherMap as example)
interface WeatherApi {
    @GET("weather")
    suspend fun getCurrentWeather(
        @Query("lat") latitude: Double,
        @Query("lon") longitude: Double,
        @Query("appid") apiKey: String,
        @Query("units") units: String = "metric",
        @Query("lang") language: String = "vi"
    ): WeatherResponse

    @GET("forecast")
    suspend fun getForecast(
        @Query("lat") latitude: Double,
        @Query("lon") longitude: Double,
        @Query("appid") apiKey: String,
        @Query("units") units: String = "metric",
        @Query("lang") language: String = "vi"
    ): ForecastResponse
}

// Data classes for API responses
data class WeatherResponse(
    val main: MainWeather,
    val weather: List<Weather>,
    val name: String
)

data class MainWeather(
    val temp: Double,
    val feels_like: Double,
    val humidity: Int
)

data class Weather(
    val main: String,
    val description: String,
    val icon: String
)

data class ForecastResponse(
    val list: List<ForecastItem>
)

data class ForecastItem(
    val dt: Long,
    val main: MainWeather,
    val weather: List<Weather>
)

@Singleton
class WeatherServiceImpl @Inject constructor(
    @ApplicationContext private val context: Context,
    private val settingsRepository: SettingsRepository
) : WeatherService {

    private val weatherApi: WeatherApi by lazy {
        Retrofit.Builder()
            .baseUrl("https://api.openweathermap.org/data/2.5/")
            .addConverterFactory(GsonConverterFactory.create())
            .build()
            .create(WeatherApi::class.java)
    }

    // You would need to get this from your app configuration
    private val apiKey = "YOUR_OPENWEATHER_API_KEY"

    override suspend fun getCurrentWeather(): WeatherData? {
        return try {
            val settings = settingsRepository.getUserSettings().first()
            val location = settings.weatherLocation?.home ?: settings.homeLocation?.let {
                LocationCoordinate(it.latitude, it.longitude)
            }

            location?.let { getWeatherForLocation(it) }
        } catch (e: Exception) {
            null
        }
    }

    override suspend fun getWeatherForLocation(location: LocationCoordinate): WeatherData? {
        return try {
            val response = weatherApi.getCurrentWeather(
                latitude = location.lat,
                longitude = location.lon,
                apiKey = apiKey
            )

            WeatherData(
                current = CurrentWeather(
                    temperature = response.main.temp,
                    description = response.weather.firstOrNull()?.description ?: "",
                    icon = response.weather.firstOrNull()?.icon ?: "",
                    location = response.name
                ),
                forecast = emptyList(), // Would be populated from forecast API
                warnings = emptyList(),
                lastUpdated = LocalDateTime.now()
            )
        } catch (e: Exception) {
            null
        }
    }

    override suspend fun getWeatherForecast(location: LocationCoordinate, days: Int): WeatherData? {
        return try {
            val currentResponse = weatherApi.getCurrentWeather(
                latitude = location.lat,
                longitude = location.lon,
                apiKey = apiKey
            )

            val forecastResponse = weatherApi.getForecast(
                latitude = location.lat,
                longitude = location.lon,
                apiKey = apiKey
            )

            val forecast = forecastResponse.list.take(days * 8).map { item -> // 8 forecasts per day (3-hour intervals)
                WeatherForecast(
                    time = LocalDateTime.ofEpochSecond(item.dt, 0, java.time.ZoneOffset.UTC),
                    temperature = item.main.temp,
                    description = item.weather.firstOrNull()?.description ?: "",
                    icon = item.weather.firstOrNull()?.icon ?: ""
                )
            }

            WeatherData(
                current = CurrentWeather(
                    temperature = currentResponse.main.temp,
                    description = currentResponse.weather.firstOrNull()?.description ?: "",
                    icon = currentResponse.weather.firstOrNull()?.icon ?: "",
                    location = currentResponse.name
                ),
                forecast = forecast,
                warnings = emptyList(),
                lastUpdated = LocalDateTime.now()
            )
        } catch (e: Exception) {
            null
        }
    }

    override suspend fun checkWeatherWarnings(
        homeLocation: LocationCoordinate?,
        workLocation: LocationCoordinate?
    ): List<WeatherWarning> {
        val warnings = mutableListOf<WeatherWarning>()

        try {
            homeLocation?.let { location ->
                val weather = getWeatherForLocation(location)
                weather?.let {
                    val homeWarnings = generateWarnings(it, WeatherWarningLocation.HOME)
                    warnings.addAll(homeWarnings)
                }
            }

            workLocation?.let { location ->
                val weather = getWeatherForLocation(location)
                weather?.let {
                    val workWarnings = generateWarnings(it, WeatherWarningLocation.WORK)
                    warnings.addAll(workWarnings)
                }
            }
        } catch (e: Exception) {
            // Handle error
        }

        return warnings
    }

    override suspend fun refreshWeatherData() {
        // Implementation to refresh cached weather data
        getCurrentWeather()
    }

    private fun generateWarnings(weather: WeatherData, location: WeatherWarningLocation): List<WeatherWarning> {
        val warnings = mutableListOf<WeatherWarning>()
        val current = weather.current

        // Temperature warnings
        when {
            current.temperature > 35 -> {
                warnings.add(
                    WeatherWarning(
                        type = WeatherWarningType.HEAT,
                        message = "Nhiệt độ cao ${current.temperature.toInt()}°C, hãy mang theo nước và tránh nắng",
                        location = location,
                        time = LocalDateTime.now()
                    )
                )
            }
            current.temperature < 10 -> {
                warnings.add(
                    WeatherWarning(
                        type = WeatherWarningType.COLD,
                        message = "Nhiệt độ thấp ${current.temperature.toInt()}°C, hãy mặc ấm",
                        location = location,
                        time = LocalDateTime.now()
                    )
                )
            }
        }

        // Rain warnings
        if (current.description.contains("rain", ignoreCase = true) || 
            current.description.contains("mưa", ignoreCase = true)) {
            warnings.add(
                WeatherWarning(
                    type = WeatherWarningType.RAIN,
                    message = "Có mưa, hãy mang theo ô",
                    location = location,
                    time = LocalDateTime.now()
                )
            )
        }

        return warnings
    }
}
