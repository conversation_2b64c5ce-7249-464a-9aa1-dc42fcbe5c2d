@echo off
echo ========================================
echo    Quick Fix for Workly Android Build
echo ========================================
echo.

echo [1/3] Cleaning project...
if exist "build" rmdir /s /q "build"
if exist "app\build" rmdir /s /q "app\build"
if exist ".gradle" rmdir /s /q ".gradle"

echo [2/3] Checking critical files...
if not exist "local.properties" (
    echo Creating local.properties...
    echo sdk.dir=C:\Users\<USER>\AppData\Local\Android\Sdk > local.properties
    echo Please edit local.properties with your correct Android SDK path!
)

echo [3/3] Files fixed:
echo ✓ Moved themes_night.xml to values-night/themes.xml
echo ✓ Created mipmap icons for all densities
echo ✓ Fixed duplicate resource issues

echo.
echo ========================================
echo Next steps:
echo 1. Edit local.properties with your Android SDK path
echo 2. Open project in Android Studio
echo 3. Let Android Studio sync and build
echo 4. Run the app
echo ========================================
echo.
pause
