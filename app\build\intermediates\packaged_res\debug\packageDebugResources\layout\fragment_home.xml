<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <!-- Current Status Card -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_current_status"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:id="@+id/text_view_current_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Sẵn sàng làm việc"
                    android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall"
                    android:textColor="?attr/colorOnSurface" />

                <TextView
                    android:id="@+id/text_view_current_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    tools:text="08:30 AM - Thứ Hai, 02/07/2025" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Multi-Function Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/button_multi_function"
            android:layout_width="0dp"
            android:layout_height="64dp"
            android:layout_marginBottom="16dp"
            android:text="Đi làm"
            android:textSize="18sp"
            app:cornerRadius="32dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/card_current_status" />

        <!-- Loading Progress Bar -->
        <ProgressBar
            android:id="@+id/progress_bar_loading"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/button_multi_function"
            app:layout_constraintEnd_toEndOf="@id/button_multi_function"
            app:layout_constraintStart_toStartOf="@id/button_multi_function"
            app:layout_constraintTop_toTopOf="@id/button_multi_function" />

        <!-- Weather Card -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_weather"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/button_multi_function">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="16dp">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/text_view_weather_temp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textAppearance="@style/TextAppearance.Material3.HeadlineMedium"
                        android:textColor="?attr/colorOnSurface"
                        tools:text="28°C" />

                    <TextView
                        android:id="@+id/text_view_weather_description"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                        android:textColor="?attr/colorOnSurfaceVariant"
                        tools:text="Nắng ít mây" />

                    <TextView
                        android:id="@+id/text_view_weather_location"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                        android:textColor="?attr/colorOnSurfaceVariant"
                        tools:text="Hà Nội" />

                </LinearLayout>

                <ImageView
                    android:id="@+id/image_view_weather_icon"
                    android:layout_width="64dp"
                    android:layout_height="64dp"
                    android:layout_gravity="center_vertical"
                    android:contentDescription="Weather icon"
                    tools:src="@drawable/ic_sunny" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Notes Section -->
        <TextView
            android:id="@+id/text_view_notes_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="Ghi chú"
            android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
            android:textColor="?attr/colorOnSurface"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/card_weather" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_view_notes"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:nestedScrollingEnabled="false"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/text_view_notes_title"
            tools:itemCount="3"
            tools:listitem="@layout/item_note" />

        <!-- Attendance History Section -->
        <TextView
            android:id="@+id/text_view_attendance_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="Lịch sử chấm công"
            android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
            android:textColor="?attr/colorOnSurface"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/recycler_view_notes" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_view_attendance_history"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:nestedScrollingEnabled="false"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/text_view_attendance_title"
            tools:itemCount="5"
            tools:listitem="@layout/item_attendance_log" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.core.widget.NestedScrollView>
