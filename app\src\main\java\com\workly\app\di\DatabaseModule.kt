package com.workly.app.di

import android.content.Context
import androidx.room.Room
import com.workly.app.data.database.WorklyDatabase
import com.workly.app.data.database.dao.AttendanceDao
import com.workly.app.data.database.dao.NoteDao
import com.workly.app.data.database.dao.ShiftDao
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {

    @Provides
    @Singleton
    fun provideWorklyDatabase(@ApplicationContext context: Context): WorklyDatabase {
        return Room.databaseBuilder(
            context,
            WorklyDatabase::class.java,
            "workly_database"
        )
            .fallbackToDestructiveMigration()
            .build()
    }

    @Provides
    fun provideShiftDao(database: WorklyDatabase): ShiftDao = database.shiftDao()

    @Provides
    fun provideAttendanceDao(database: WorklyDatabase): AttendanceDao = database.attendanceDao()

    @Provides
    fun provideNoteDao(database: WorklyDatabase): NoteDao = database.noteDao()
}
