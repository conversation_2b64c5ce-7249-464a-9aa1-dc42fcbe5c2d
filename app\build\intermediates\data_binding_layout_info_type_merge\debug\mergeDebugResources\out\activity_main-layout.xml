<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.workly.app" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout" rootNodeViewId="@+id/container"><Targets><Target id="@+id/container" tag="layout/activity_main_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="31" endOffset="51"/></Target><Target id="@+id/nav_view" view="com.google.android.material.bottomnavigation.BottomNavigationView"><Expressions/><location startLine="7" startOffset="4" endLine="17" endOffset="42"/></Target></Targets></Layout>