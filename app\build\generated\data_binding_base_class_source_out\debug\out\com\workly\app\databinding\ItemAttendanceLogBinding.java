// Generated by view binder compiler. Do not edit!
package com.workly.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.card.MaterialCardView;
import com.workly.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemAttendanceLogBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final TextView textViewAutoGenerated;

  @NonNull
  public final TextView textViewDate;

  @NonNull
  public final TextView textViewLocation;

  @NonNull
  public final TextView textViewNote;

  @NonNull
  public final TextView textViewTime;

  @NonNull
  public final TextView textViewType;

  @NonNull
  public final View viewTypeIndicator;

  private ItemAttendanceLogBinding(@NonNull MaterialCardView rootView,
      @NonNull TextView textViewAutoGenerated, @NonNull TextView textViewDate,
      @NonNull TextView textViewLocation, @NonNull TextView textViewNote,
      @NonNull TextView textViewTime, @NonNull TextView textViewType,
      @NonNull View viewTypeIndicator) {
    this.rootView = rootView;
    this.textViewAutoGenerated = textViewAutoGenerated;
    this.textViewDate = textViewDate;
    this.textViewLocation = textViewLocation;
    this.textViewNote = textViewNote;
    this.textViewTime = textViewTime;
    this.textViewType = textViewType;
    this.viewTypeIndicator = viewTypeIndicator;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemAttendanceLogBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemAttendanceLogBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_attendance_log, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemAttendanceLogBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.text_view_auto_generated;
      TextView textViewAutoGenerated = ViewBindings.findChildViewById(rootView, id);
      if (textViewAutoGenerated == null) {
        break missingId;
      }

      id = R.id.text_view_date;
      TextView textViewDate = ViewBindings.findChildViewById(rootView, id);
      if (textViewDate == null) {
        break missingId;
      }

      id = R.id.text_view_location;
      TextView textViewLocation = ViewBindings.findChildViewById(rootView, id);
      if (textViewLocation == null) {
        break missingId;
      }

      id = R.id.text_view_note;
      TextView textViewNote = ViewBindings.findChildViewById(rootView, id);
      if (textViewNote == null) {
        break missingId;
      }

      id = R.id.text_view_time;
      TextView textViewTime = ViewBindings.findChildViewById(rootView, id);
      if (textViewTime == null) {
        break missingId;
      }

      id = R.id.text_view_type;
      TextView textViewType = ViewBindings.findChildViewById(rootView, id);
      if (textViewType == null) {
        break missingId;
      }

      id = R.id.view_type_indicator;
      View viewTypeIndicator = ViewBindings.findChildViewById(rootView, id);
      if (viewTypeIndicator == null) {
        break missingId;
      }

      return new ItemAttendanceLogBinding((MaterialCardView) rootView, textViewAutoGenerated,
          textViewDate, textViewLocation, textViewNote, textViewTime, textViewType,
          viewTypeIndicator);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
