package com.workly.app.data.model

import android.os.Parcelable
import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.parcelize.Parcelize
import java.time.LocalDateTime

@Parcelize
@Entity(tableName = "attendance_logs")
data class AttendanceLog(
    @PrimaryKey
    val id: String,
    val type: AttendanceType,
    val time: LocalDateTime,
    val timestamp: LocalDateTime? = null, // For auto-generated logs
    val shiftId: String? = null, // Associated shift ID
    val location: String? = null, // Location info
    val isAutoGenerated: Boolean = false, // Flag for auto-generated logs
    val note: String? = null // Optional note
) : Parcelable

enum class AttendanceType {
    GO_WORK,
    CHECK_IN,
    PUNCH,
    CHECK_OUT,
    COMPLETE
}

// Extension functions
fun AttendanceLog.getDisplayText(): String {
    return when (type) {
        AttendanceType.GO_WORK -> "Đi làm"
        AttendanceType.CHECK_IN -> "Vào làm"
        AttendanceType.PUNCH -> "Chấm công"
        AttendanceType.CHECK_OUT -> "Ra về"
        AttendanceType.COMPLETE -> "Hoàn thành"
    }
}

fun AttendanceLog.getDisplayColor(): Int {
    return when (type) {
        AttendanceType.GO_WORK -> android.graphics.Color.BLUE
        AttendanceType.CHECK_IN -> android.graphics.Color.GREEN
        AttendanceType.PUNCH -> android.graphics.Color.YELLOW
        AttendanceType.CHECK_OUT -> android.graphics.Color.MAGENTA
        AttendanceType.COMPLETE -> android.graphics.Color.GRAY
    }
}
