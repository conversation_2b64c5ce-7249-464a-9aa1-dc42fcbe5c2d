-- Merging decision tree log ---
provider#androidx.startup.InitializationProvider
INJECTED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:67:9-75:20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87b27440a33b3dcf7fa56b0a1a24dfeb\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87b27440a33b3dcf7fa56b0a1a24dfeb\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4a3ab3a1a836618d2322c30118e7a126\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4a3ab3a1a836618d2322c30118e7a126\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\345e2012fc3e6c58a07252456d06f0cc\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\345e2012fc3e6c58a07252456d06f0cc\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:71:13-31
	android:authorities
		INJECTED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:69:13-68
	android:exported
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:70:13-37
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:68:13-67
manifest
ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:2:1-79:12
INJECTED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:2:1-79:12
INJECTED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:2:1-79:12
INJECTED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:2:1-79:12
MERGED from [androidx.databinding:databinding-adapters:8.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fc8b7235d4e6810b19bd4b19bf213a90\transformed\databinding-adapters-8.11.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-ktx:8.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\69454cb3f1d38caf0cd3aaa8e91c5c7d\transformed\databinding-ktx-8.11.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-runtime:8.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\410fdf0bfd9998ebdd34356ffe0ca3dc\transformed\databinding-runtime-8.11.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:viewbinding:8.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\944d50a5ae526d76455ff629b738f5b1\transformed\viewbinding-8.11.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.preference:preference-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\32f6b5e2a46e9f7db84091fb5316fede\transformed\preference-ktx-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\374ac12534acb8d29e7882ef759acf2e\transformed\navigation-common-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\be04489b7ed79e3ff7c267c6f7b16b76\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\f06eefd9aa7380f9b27edce63b24b458\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d1200cc83344a5c5f5a5b7781a25206\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-fragment:2.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\46bc6132f8fec459ccad63b1dc23233f\transformed\navigation-fragment-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef2aaf62f2bfeccff0bb0fc5f37b6166\transformed\navigation-fragment-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\856fe1c14d06ddd88da6fe4b303e45a7\transformed\navigation-ui-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui:2.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\dbb4439497655819a5bbf0c365938793\transformed\navigation-ui-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f72d64ff0a85ad35d4f32776e9fe964a\transformed\material-1.11.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0ecbb4b61badaee0a6bb8dc6b4b88705\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ef77ae89540ff5c7006e0adc0cc7641\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dcc97860fb5a05a9300bca7d380ad952\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\14e1ed44b3025a4e79fd4f5fe03e4452\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\8.13\transforms\029b7e7b714c371da32d48c9a835450b\transformed\hilt-android-2.48\AndroidManifest.xml:16:1-19:12
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc77bc2feb0e2b4b8145e3e0f896677f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:17:1-44:12
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\30f48abc0d4f372de3861a5f866e237b\transformed\play-services-location-21.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3678371e4b9f291b0b6ff6bca1ac707a\transformed\play-services-base-18.1.0\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea06be59a3f0a6ea92887231d5ed0dab\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\240c634c8d2ef38e2610db507fc6f6e4\transformed\play-services-basement-18.1.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\33692d71f6b7465a1fa43ecff2142b1e\transformed\recyclerview-1.3.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.13\transforms\208e7d2af1454c788788656bd921c67e\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\07e183d75851398ee48d27ce7fe16bd0\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\20701cf9eadf0a49fa79394fc135c115\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd76fa834efb3397b062b88ab2105096\transformed\activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f232005e111e305fc6cdb436c3ae21cc\transformed\activity-ktx-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cfe90f03ac9d97d22733b11a16d14b1e\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87b27440a33b3dcf7fa56b0a1a24dfeb\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\95ef829f7cdd0903e821f7dec39693ee\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a50c59fdb9a70e35300050366f08a18e\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0cc6feb6289d4988978ddff093838387\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\352773d6bfdc5acd72186ff4fa389015\transformed\work-runtime-ktx-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5912a8cfb8549fec6a5e822a4f796689\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7cab010bbff01a965c0f8cdfcf327830\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93c30e5c1e130f64097307034cbde548\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5b81685cc397a8ad62c8aace780a8b69\transformed\core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7ad2725f9bc57aacb23b94495684f65\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\559f5b5eba529f905aa86598ee10818f\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ecd79e27684a6dbde9608d3ab1d0ba8\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b090e3fef5b89c9577416afbb41f6e77\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc23e714a652031ff9ea78eb6cabb3d\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\44b260706e6157ce1b935ac893a37604\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0bda7d7fbdb602b0f7cbefd3f2f2380c\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6c1f7dfac5c384226ae98ee9bd52ff7\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e836646ffe6ee3a4d8fb0be5b0a8fa86\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2462634eb968dc701f0618ffbec7ca87\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3deeef4830ac5f9d446929efca22e92e\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84ec0ce9a05b1e6837aff9f99a1dfa92\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8af33194f3dfc2f8bc08d7753e6a12a4\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c6f3df8d87d77e49cf8c06d86542474\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4a3ab3a1a836618d2322c30118e7a126\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\78ed2d2707311fbe46d3be238f0af706\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f003ac91bb03402a92649b14e0ab58d6\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e80eedf4138903a9cf26eac31c0ae50a\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b302fe0c9158945018cc21cb7c8cfed\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14a837b5b137487903e811269b6896ff\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\595dd085658feb0d694096590e6390c8\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb3e658c23c1e3a637ad69981f7e53a5\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f956862b552c094880a5ede0d394be08\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d828bd375308fd9adb1de05cbf9cce6\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d291327265658ae16c1162039232476\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a5b1066eb5507ba3fadc6f324be20f9b\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ded7950122be8ccfe9c738e53d39a39\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e24d53e7f60be24bdbd90c918e349815\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0c287a1575e666c165dad78185bb43e5\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6187a537d34915bc5246d92d082acbf\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd00bfae86a6764b0dabd5022d654f97\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\345e2012fc3e6c58a07252456d06f0cc\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd528234b9d6d66b9d293d9707d89026\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e5eada78cdb75873f2b0d31b2d21f19\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f8d58972c775b3824aa4f9b1066c96ef\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c036124b28b600ac5d014c373a06ba5\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\8.13\transforms\b26bd7c1c559bd2d500cd1eb1c872c03\transformed\dagger-lint-aar-2.48\AndroidManifest.xml:16:1-19:12
	package
		INJECTED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:6:5-79
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:6:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:7:5-81
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:7:22-78
uses-permission#android.permission.VIBRATE
ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:8:5-66
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:8:22-63
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:9:5-81
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:9:22-78
uses-permission#android.permission.WAKE_LOCK
ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:10:5-68
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:5-68
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:5-68
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:10:22-65
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:11:5-77
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:11:22-74
uses-permission#android.permission.INTERNET
ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:12:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc77bc2feb0e2b4b8145e3e0f896677f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc77bc2feb0e2b4b8145e3e0f896677f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:12:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:13:5-79
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc77bc2feb0e2b4b8145e3e0f896677f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc77bc2feb0e2b4b8145e3e0f896677f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:24:5-79
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:13:22-76
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:14:5-77
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:14:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_LOCATION
ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:15:5-86
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:15:22-83
application
ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:17:5-77:19
INJECTED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:17:5-77:19
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f72d64ff0a85ad35d4f32776e9fe964a\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f72d64ff0a85ad35d4f32776e9fe964a\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0ecbb4b61badaee0a6bb8dc6b4b88705\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0ecbb4b61badaee0a6bb8dc6b4b88705\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc77bc2feb0e2b4b8145e3e0f896677f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc77bc2feb0e2b4b8145e3e0f896677f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\30f48abc0d4f372de3861a5f866e237b\transformed\play-services-location-21.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\30f48abc0d4f372de3861a5f866e237b\transformed\play-services-location-21.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3678371e4b9f291b0b6ff6bca1ac707a\transformed\play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3678371e4b9f291b0b6ff6bca1ac707a\transformed\play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea06be59a3f0a6ea92887231d5ed0dab\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea06be59a3f0a6ea92887231d5ed0dab\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\240c634c8d2ef38e2610db507fc6f6e4\transformed\play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\240c634c8d2ef38e2610db507fc6f6e4\transformed\play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87b27440a33b3dcf7fa56b0a1a24dfeb\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87b27440a33b3dcf7fa56b0a1a24dfeb\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84ec0ce9a05b1e6837aff9f99a1dfa92\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84ec0ce9a05b1e6837aff9f99a1dfa92\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8af33194f3dfc2f8bc08d7753e6a12a4\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8af33194f3dfc2f8bc08d7753e6a12a4\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4a3ab3a1a836618d2322c30118e7a126\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4a3ab3a1a836618d2322c30118e7a126\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb3e658c23c1e3a637ad69981f7e53a5\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb3e658c23c1e3a637ad69981f7e53a5\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd00bfae86a6764b0dabd5022d654f97\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd00bfae86a6764b0dabd5022d654f97\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\345e2012fc3e6c58a07252456d06f0cc\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\345e2012fc3e6c58a07252456d06f0cc\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8af33194f3dfc2f8bc08d7753e6a12a4\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:25:9-35
	android:label
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:23:9-41
	android:fullBackupContent
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:21:9-54
	android:roundIcon
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:24:9-54
	tools:targetApi
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:27:9-29
	android:icon
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:22:9-43
	android:allowBackup
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:19:9-35
	android:theme
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:26:9-44
	android:dataExtractionRules
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:20:9-65
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:18:9-42
activity#com.workly.app.ui.MainActivity
ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:30:9-38:20
	android:exported
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:32:13-36
	android:theme
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:33:13-60
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:31:13-44
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:34:13-37:29
action#android.intent.action.MAIN
ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:35:17-69
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:35:25-66
category#android.intent.category.LAUNCHER
ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:36:17-77
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:36:27-74
receiver#com.workly.app.receivers.BootReceiver
ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:41:9-51:20
	android:enabled
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:43:13-35
	android:exported
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:44:13-36
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:42:13-51
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.MY_PACKAGE_REPLACED+action:name:android.intent.action.PACKAGE_REPLACED+data:scheme:package
ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:45:13-50:29
	android:priority
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:45:28-51
action#android.intent.action.BOOT_COMPLETED
ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:46:17-79
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:46:25-76
action#android.intent.action.MY_PACKAGE_REPLACED
ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:47:17-84
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:47:25-81
action#android.intent.action.PACKAGE_REPLACED
ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:48:17-81
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:48:25-78
data
ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:49:17-50
	android:scheme
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:49:23-47
receiver#com.workly.app.receivers.AlarmReceiver
ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:54:9-57:40
	android:enabled
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:56:13-35
	android:exported
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:57:13-37
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:55:13-52
service#com.workly.app.services.LocationTrackingService
ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:60:9-64:56
	android:enabled
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:62:13-35
	android:exported
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:63:13-37
	android:foregroundServiceType
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:64:13-53
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:61:13-61
meta-data#androidx.work.WorkManagerInitializer
ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:72:13-74:52
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:74:17-49
	android:name
		ADDED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml:73:17-68
uses-sdk
INJECTED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml
INJECTED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:databinding-adapters:8.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fc8b7235d4e6810b19bd4b19bf213a90\transformed\databinding-adapters-8.11.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-adapters:8.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fc8b7235d4e6810b19bd4b19bf213a90\transformed\databinding-adapters-8.11.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:8.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\69454cb3f1d38caf0cd3aaa8e91c5c7d\transformed\databinding-ktx-8.11.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:8.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\69454cb3f1d38caf0cd3aaa8e91c5c7d\transformed\databinding-ktx-8.11.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:8.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\410fdf0bfd9998ebdd34356ffe0ca3dc\transformed\databinding-runtime-8.11.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:8.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\410fdf0bfd9998ebdd34356ffe0ca3dc\transformed\databinding-runtime-8.11.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\944d50a5ae526d76455ff629b738f5b1\transformed\viewbinding-8.11.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\944d50a5ae526d76455ff629b738f5b1\transformed\viewbinding-8.11.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.preference:preference-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\32f6b5e2a46e9f7db84091fb5316fede\transformed\preference-ktx-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\32f6b5e2a46e9f7db84091fb5316fede\transformed\preference-ktx-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\374ac12534acb8d29e7882ef759acf2e\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\374ac12534acb8d29e7882ef759acf2e\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\be04489b7ed79e3ff7c267c6f7b16b76\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\be04489b7ed79e3ff7c267c6f7b16b76\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\f06eefd9aa7380f9b27edce63b24b458\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\f06eefd9aa7380f9b27edce63b24b458\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d1200cc83344a5c5f5a5b7781a25206\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d1200cc83344a5c5f5a5b7781a25206\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\46bc6132f8fec459ccad63b1dc23233f\transformed\navigation-fragment-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\46bc6132f8fec459ccad63b1dc23233f\transformed\navigation-fragment-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef2aaf62f2bfeccff0bb0fc5f37b6166\transformed\navigation-fragment-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef2aaf62f2bfeccff0bb0fc5f37b6166\transformed\navigation-fragment-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\856fe1c14d06ddd88da6fe4b303e45a7\transformed\navigation-ui-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\856fe1c14d06ddd88da6fe4b303e45a7\transformed\navigation-ui-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\dbb4439497655819a5bbf0c365938793\transformed\navigation-ui-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\dbb4439497655819a5bbf0c365938793\transformed\navigation-ui-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f72d64ff0a85ad35d4f32776e9fe964a\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f72d64ff0a85ad35d4f32776e9fe964a\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0ecbb4b61badaee0a6bb8dc6b4b88705\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0ecbb4b61badaee0a6bb8dc6b4b88705\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ef77ae89540ff5c7006e0adc0cc7641\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ef77ae89540ff5c7006e0adc0cc7641\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dcc97860fb5a05a9300bca7d380ad952\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dcc97860fb5a05a9300bca7d380ad952\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\14e1ed44b3025a4e79fd4f5fe03e4452\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\14e1ed44b3025a4e79fd4f5fe03e4452\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\8.13\transforms\029b7e7b714c371da32d48c9a835450b\transformed\hilt-android-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\8.13\transforms\029b7e7b714c371da32d48c9a835450b\transformed\hilt-android-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc77bc2feb0e2b4b8145e3e0f896677f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc77bc2feb0e2b4b8145e3e0f896677f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\30f48abc0d4f372de3861a5f866e237b\transformed\play-services-location-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\30f48abc0d4f372de3861a5f866e237b\transformed\play-services-location-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3678371e4b9f291b0b6ff6bca1ac707a\transformed\play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3678371e4b9f291b0b6ff6bca1ac707a\transformed\play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea06be59a3f0a6ea92887231d5ed0dab\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea06be59a3f0a6ea92887231d5ed0dab\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\240c634c8d2ef38e2610db507fc6f6e4\transformed\play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\240c634c8d2ef38e2610db507fc6f6e4\transformed\play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\33692d71f6b7465a1fa43ecff2142b1e\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\33692d71f6b7465a1fa43ecff2142b1e\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.13\transforms\208e7d2af1454c788788656bd921c67e\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.13\transforms\208e7d2af1454c788788656bd921c67e\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\07e183d75851398ee48d27ce7fe16bd0\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\07e183d75851398ee48d27ce7fe16bd0\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\20701cf9eadf0a49fa79394fc135c115\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\20701cf9eadf0a49fa79394fc135c115\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd76fa834efb3397b062b88ab2105096\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd76fa834efb3397b062b88ab2105096\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f232005e111e305fc6cdb436c3ae21cc\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f232005e111e305fc6cdb436c3ae21cc\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cfe90f03ac9d97d22733b11a16d14b1e\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cfe90f03ac9d97d22733b11a16d14b1e\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87b27440a33b3dcf7fa56b0a1a24dfeb\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87b27440a33b3dcf7fa56b0a1a24dfeb\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\95ef829f7cdd0903e821f7dec39693ee\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\95ef829f7cdd0903e821f7dec39693ee\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a50c59fdb9a70e35300050366f08a18e\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a50c59fdb9a70e35300050366f08a18e\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0cc6feb6289d4988978ddff093838387\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0cc6feb6289d4988978ddff093838387\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\352773d6bfdc5acd72186ff4fa389015\transformed\work-runtime-ktx-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\352773d6bfdc5acd72186ff4fa389015\transformed\work-runtime-ktx-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5912a8cfb8549fec6a5e822a4f796689\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5912a8cfb8549fec6a5e822a4f796689\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7cab010bbff01a965c0f8cdfcf327830\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7cab010bbff01a965c0f8cdfcf327830\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93c30e5c1e130f64097307034cbde548\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93c30e5c1e130f64097307034cbde548\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5b81685cc397a8ad62c8aace780a8b69\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5b81685cc397a8ad62c8aace780a8b69\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7ad2725f9bc57aacb23b94495684f65\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7ad2725f9bc57aacb23b94495684f65\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\559f5b5eba529f905aa86598ee10818f\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\559f5b5eba529f905aa86598ee10818f\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ecd79e27684a6dbde9608d3ab1d0ba8\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ecd79e27684a6dbde9608d3ab1d0ba8\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b090e3fef5b89c9577416afbb41f6e77\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b090e3fef5b89c9577416afbb41f6e77\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc23e714a652031ff9ea78eb6cabb3d\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc23e714a652031ff9ea78eb6cabb3d\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\44b260706e6157ce1b935ac893a37604\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\44b260706e6157ce1b935ac893a37604\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0bda7d7fbdb602b0f7cbefd3f2f2380c\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0bda7d7fbdb602b0f7cbefd3f2f2380c\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6c1f7dfac5c384226ae98ee9bd52ff7\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6c1f7dfac5c384226ae98ee9bd52ff7\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e836646ffe6ee3a4d8fb0be5b0a8fa86\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e836646ffe6ee3a4d8fb0be5b0a8fa86\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2462634eb968dc701f0618ffbec7ca87\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2462634eb968dc701f0618ffbec7ca87\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3deeef4830ac5f9d446929efca22e92e\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3deeef4830ac5f9d446929efca22e92e\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84ec0ce9a05b1e6837aff9f99a1dfa92\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84ec0ce9a05b1e6837aff9f99a1dfa92\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8af33194f3dfc2f8bc08d7753e6a12a4\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8af33194f3dfc2f8bc08d7753e6a12a4\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c6f3df8d87d77e49cf8c06d86542474\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8c6f3df8d87d77e49cf8c06d86542474\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4a3ab3a1a836618d2322c30118e7a126\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4a3ab3a1a836618d2322c30118e7a126\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\78ed2d2707311fbe46d3be238f0af706\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\78ed2d2707311fbe46d3be238f0af706\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f003ac91bb03402a92649b14e0ab58d6\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f003ac91bb03402a92649b14e0ab58d6\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e80eedf4138903a9cf26eac31c0ae50a\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e80eedf4138903a9cf26eac31c0ae50a\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b302fe0c9158945018cc21cb7c8cfed\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b302fe0c9158945018cc21cb7c8cfed\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14a837b5b137487903e811269b6896ff\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14a837b5b137487903e811269b6896ff\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\595dd085658feb0d694096590e6390c8\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\595dd085658feb0d694096590e6390c8\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb3e658c23c1e3a637ad69981f7e53a5\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb3e658c23c1e3a637ad69981f7e53a5\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f956862b552c094880a5ede0d394be08\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f956862b552c094880a5ede0d394be08\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d828bd375308fd9adb1de05cbf9cce6\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d828bd375308fd9adb1de05cbf9cce6\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d291327265658ae16c1162039232476\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d291327265658ae16c1162039232476\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a5b1066eb5507ba3fadc6f324be20f9b\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a5b1066eb5507ba3fadc6f324be20f9b\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ded7950122be8ccfe9c738e53d39a39\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ded7950122be8ccfe9c738e53d39a39\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e24d53e7f60be24bdbd90c918e349815\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e24d53e7f60be24bdbd90c918e349815\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0c287a1575e666c165dad78185bb43e5\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0c287a1575e666c165dad78185bb43e5\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6187a537d34915bc5246d92d082acbf\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6187a537d34915bc5246d92d082acbf\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd00bfae86a6764b0dabd5022d654f97\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd00bfae86a6764b0dabd5022d654f97\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\345e2012fc3e6c58a07252456d06f0cc\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\345e2012fc3e6c58a07252456d06f0cc\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd528234b9d6d66b9d293d9707d89026\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd528234b9d6d66b9d293d9707d89026\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e5eada78cdb75873f2b0d31b2d21f19\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e5eada78cdb75873f2b0d31b2d21f19\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f8d58972c775b3824aa4f9b1066c96ef\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f8d58972c775b3824aa4f9b1066c96ef\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c036124b28b600ac5d014c373a06ba5\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c036124b28b600ac5d014c373a06ba5\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\8.13\transforms\b26bd7c1c559bd2d500cd1eb1c872c03\transformed\dagger-lint-aar-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\8.13\transforms\b26bd7c1c559bd2d500cd1eb1c872c03\transformed\dagger-lint-aar-2.48\AndroidManifest.xml:18:3-42
	android:targetSdkVersion
		INJECTED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from G:\IT\PROJECT-IDEAL\workly_android\app\src\main\AndroidManifest.xml
uses-feature#0x00020000
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc77bc2feb0e2b4b8145e3e0f896677f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
	android:glEsVersion
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc77bc2feb0e2b4b8145e3e0f896677f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc77bc2feb0e2b4b8145e3e0f896677f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
queries
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc77bc2feb0e2b4b8145e3e0f896677f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
package#com.google.android.apps.maps
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc77bc2feb0e2b4b8145e3e0f896677f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc77bc2feb0e2b4b8145e3e0f896677f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
uses-library#org.apache.http.legacy
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc77bc2feb0e2b4b8145e3e0f896677f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc77bc2feb0e2b4b8145e3e0f896677f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc77bc2feb0e2b4b8145e3e0f896677f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3678371e4b9f291b0b6ff6bca1ac707a\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3678371e4b9f291b0b6ff6bca1ac707a\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3678371e4b9f291b0b6ff6bca1ac707a\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3678371e4b9f291b0b6ff6bca1ac707a\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\240c634c8d2ef38e2610db507fc6f6e4\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\240c634c8d2ef38e2610db507fc6f6e4\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\240c634c8d2ef38e2610db507fc6f6e4\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87b27440a33b3dcf7fa56b0a1a24dfeb\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87b27440a33b3dcf7fa56b0a1a24dfeb\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\87b27440a33b3dcf7fa56b0a1a24dfeb\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bebf47af6f5eea5f3e7f57ea4302aa2\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84ec0ce9a05b1e6837aff9f99a1dfa92\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84ec0ce9a05b1e6837aff9f99a1dfa92\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84ec0ce9a05b1e6837aff9f99a1dfa92\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84ec0ce9a05b1e6837aff9f99a1dfa92\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84ec0ce9a05b1e6837aff9f99a1dfa92\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84ec0ce9a05b1e6837aff9f99a1dfa92\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8af33194f3dfc2f8bc08d7753e6a12a4\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8af33194f3dfc2f8bc08d7753e6a12a4\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8af33194f3dfc2f8bc08d7753e6a12a4\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.workly.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8af33194f3dfc2f8bc08d7753e6a12a4\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8af33194f3dfc2f8bc08d7753e6a12a4\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8af33194f3dfc2f8bc08d7753e6a12a4\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8af33194f3dfc2f8bc08d7753e6a12a4\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8af33194f3dfc2f8bc08d7753e6a12a4\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.workly.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8af33194f3dfc2f8bc08d7753e6a12a4\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8af33194f3dfc2f8bc08d7753e6a12a4\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4a3ab3a1a836618d2322c30118e7a126\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4a3ab3a1a836618d2322c30118e7a126\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4a3ab3a1a836618d2322c30118e7a126\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb3e658c23c1e3a637ad69981f7e53a5\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb3e658c23c1e3a637ad69981f7e53a5\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb3e658c23c1e3a637ad69981f7e53a5\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb3e658c23c1e3a637ad69981f7e53a5\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb3e658c23c1e3a637ad69981f7e53a5\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67253a3b5ac5dfc79c48dd2d80cc7bdc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
