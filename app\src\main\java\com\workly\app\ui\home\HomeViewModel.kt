package com.workly.app.ui.home

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.workly.app.data.model.AttendanceLog
import com.workly.app.data.model.Note
import com.workly.app.data.model.WeatherData
import com.workly.app.data.repository.AttendanceRepository
import com.workly.app.data.repository.NoteRepository
import com.workly.app.data.repository.SettingsRepository
import com.workly.app.services.LocationService
import com.workly.app.services.NotificationService
import com.workly.app.services.WeatherService
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import java.time.LocalDateTime
import javax.inject.Inject

@HiltViewModel
class HomeViewModel @Inject constructor(
    private val attendanceRepository: AttendanceRepository,
    private val noteRepository: NoteRepository,
    private val settingsRepository: SettingsRepository,
    private val locationService: LocationService,
    private val notificationService: NotificationService,
    private val weatherService: WeatherService
) : ViewModel() {

    private val _uiState = MutableStateFlow(HomeUiState())
    val uiState: StateFlow<HomeUiState> = _uiState.asStateFlow()

    private val _attendanceHistory = MutableStateFlow<List<AttendanceLog>>(emptyList())
    val attendanceHistory: StateFlow<List<AttendanceLog>> = _attendanceHistory.asStateFlow()

    private val _activeNotes = MutableStateFlow<List<Note>>(emptyList())
    val activeNotes: StateFlow<List<Note>> = _activeNotes.asStateFlow()

    private val _weatherData = MutableStateFlow<WeatherData?>(null)
    val weatherData: StateFlow<WeatherData?> = _weatherData.asStateFlow()

    init {
        loadData()
    }

    private fun loadData() {
        loadAttendanceHistory()
        loadActiveNotes()
        loadWeatherData()
        updateButtonState()
    }

    private fun loadAttendanceHistory() {
        viewModelScope.launch {
            val fromDate = LocalDateTime.now().minusDays(7)
            attendanceRepository.getRecentLogsFlow(fromDate, 10).collectLatest { logs ->
                _attendanceHistory.value = logs
            }
        }
    }

    private fun loadActiveNotes() {
        viewModelScope.launch {
            noteRepository.getActiveNotesFlow().collectLatest { notes ->
                _activeNotes.value = notes.take(3) // Show only first 3 notes
            }
        }
    }

    private fun loadWeatherData() {
        viewModelScope.launch {
            try {
                val weather = weatherService.getCurrentWeather()
                _weatherData.value = weather
            } catch (e: Exception) {
                // Handle weather loading error
            }
        }
    }

    private fun updateButtonState() {
        viewModelScope.launch {
            // Logic to determine button state based on current time, shift, and attendance logs
            val currentState = determineButtonState()
            _uiState.value = _uiState.value.copy(
                buttonText = getButtonText(currentState),
                buttonEnabled = true,
                currentStatus = getCurrentStatusText()
            )
        }
    }

    fun handleMultiFunctionButtonClick() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            try {
                // Handle button click based on current state
                val currentState = determineButtonState()
                when (currentState) {
                    ButtonState.GO_WORK -> handleGoWork()
                    ButtonState.CHECK_IN -> handleCheckIn()
                    ButtonState.CHECK_OUT -> handleCheckOut()
                    ButtonState.COMPLETED_DAY -> handleCompletedDay()
                    else -> {}
                }
            } catch (e: Exception) {
                // Handle error
            } finally {
                _uiState.value = _uiState.value.copy(isLoading = false)
            }
        }
    }

    fun snoozeNote(note: Note) {
        viewModelScope.launch {
            val snoozedNote = note.copy(
                snoozeUntil = LocalDateTime.now().plusHours(1)
            )
            noteRepository.updateNote(snoozedNote)
        }
    }

    private fun determineButtonState(): ButtonState {
        // Logic to determine current button state
        // This would check current time, active shift, recent attendance logs, etc.
        return ButtonState.GO_WORK // Placeholder
    }

    private fun getButtonText(state: ButtonState): String {
        return when (state) {
            ButtonState.GO_WORK -> "Đi làm"
            ButtonState.CHECK_IN -> "Vào làm"
            ButtonState.CHECK_OUT -> "Ra về"
            ButtonState.COMPLETED_DAY -> "Hoàn thành"
            else -> "Chấm công"
        }
    }

    private fun getCurrentStatusText(): String {
        // Logic to get current work status text
        return "Sẵn sàng làm việc" // Placeholder
    }

    private suspend fun handleGoWork() {
        // Implementation for "Go Work" action
    }

    private suspend fun handleCheckIn() {
        // Implementation for "Check In" action
    }

    private suspend fun handleCheckOut() {
        // Implementation for "Check Out" action
    }

    private suspend fun handleCompletedDay() {
        // Implementation for "Completed Day" action
    }
}

data class HomeUiState(
    val isLoading: Boolean = false,
    val buttonText: String = "Đi làm",
    val buttonEnabled: Boolean = true,
    val currentStatus: String = "Sẵn sàng làm việc",
    val error: String? = null
)

enum class ButtonState {
    GO_WORK,
    AWAITING_CHECK_IN,
    CHECK_IN,
    CHECK_OUT,
    COMPLETED_DAY
}
