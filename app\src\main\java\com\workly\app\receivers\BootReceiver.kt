package com.workly.app.receivers

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.WorkManager
import com.workly.app.workers.RestoreAlarmsWorker

class BootReceiver : BroadcastReceiver() {

    override fun onReceive(context: Context, intent: Intent) {
        when (intent.action) {
            Intent.ACTION_BOOT_COMPLETED,
            Intent.ACTION_MY_PACKAGE_REPLACED,
            Intent.ACTION_PACKAGE_REPLACED -> {
                // Restore alarms and notifications after device boot or app update
                restoreAlarms(context)
            }
        }
    }

    private fun restoreAlarms(context: Context) {
        val workRequest = OneTimeWorkRequestBuilder<RestoreAlarmsWorker>()
            .build()

        WorkManager.getInstance(context).enqueue(workRequest)
    }
}
