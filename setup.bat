@echo off
echo ========================================
echo    Workly Android Setup Script
echo ========================================
echo.

REM Check if local.properties exists
if not exist "local.properties" (
    echo [1/4] Creating local.properties from template...
    if exist "local.properties.template" (
        copy "local.properties.template" "local.properties"
        echo ✓ local.properties created. Please edit it with your SDK path.
    ) else (
        echo ✗ local.properties.template not found!
        echo Please create local.properties manually with your Android SDK path.
    )
) else (
    echo [1/4] ✓ local.properties already exists
)
echo.

REM Check if gradle wrapper jar exists
if not exist "gradle\wrapper\gradle-wrapper.jar" (
    echo [2/4] Gradle wrapper jar not found. 
    echo Please open this project in Android Studio to auto-generate it,
    echo or manually download gradle-wrapper.jar and place it in gradle/wrapper/
) else (
    echo [2/4] ✓ Gradle wrapper jar exists
)
echo.

REM Try to sync gradle
echo [3/4] Attempting Gradle sync...
if exist "gradlew.bat" (
    if exist "gradle\wrapper\gradle-wrapper.jar" (
        echo Running gradlew tasks...
        call gradlew.bat tasks --quiet
        if %ERRORLEVEL% EQU 0 (
            echo ✓ Gradle sync successful
        ) else (
            echo ✗ Gradle sync failed. Please check your setup.
        )
    ) else (
        echo ✗ Cannot run Gradle without wrapper jar
    )
) else (
    echo ✗ gradlew.bat not found
)
echo.

echo [4/4] Setup Summary:
echo ========================================
if exist "local.properties" echo ✓ local.properties: OK
if not exist "local.properties" echo ✗ local.properties: MISSING
if exist "gradle\wrapper\gradle-wrapper.jar" echo ✓ Gradle wrapper: OK
if not exist "gradle\wrapper\gradle-wrapper.jar" echo ✗ Gradle wrapper: MISSING
echo.

echo Next steps:
echo 1. Edit local.properties with your Android SDK path
echo 2. Open project in Android Studio
echo 3. Let Android Studio sync the project
echo 4. Build and run the app
echo.
echo For detailed instructions, see SETUP.md
echo.
pause
