<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_attendance_log" modulePackage="com.workly.app" filePath="app\src\main\res\layout\item_attendance_log.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_attendance_log_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="121" endOffset="51"/></Target><Target id="@+id/view_type_indicator" view="View"><Expressions/><location startLine="17" startOffset="8" endLine="24" endOffset="55"/></Target><Target id="@+id/text_view_type" view="TextView"><Expressions/><location startLine="27" startOffset="8" endLine="38" endOffset="34"/></Target><Target id="@+id/text_view_time" view="TextView"><Expressions/><location startLine="41" startOffset="8" endLine="49" endOffset="32"/></Target><Target id="@+id/text_view_date" view="TextView"><Expressions/><location startLine="52" startOffset="8" endLine="61" endOffset="37"/></Target><Target id="@+id/text_view_location" view="TextView"><Expressions/><location startLine="64" startOffset="8" endLine="80" endOffset="40"/></Target><Target id="@+id/text_view_note" view="TextView"><Expressions/><location startLine="83" startOffset="8" endLine="99" endOffset="40"/></Target><Target id="@+id/text_view_auto_generated" view="TextView"><Expressions/><location startLine="102" startOffset="8" endLine="117" endOffset="40"/></Target></Targets></Layout>