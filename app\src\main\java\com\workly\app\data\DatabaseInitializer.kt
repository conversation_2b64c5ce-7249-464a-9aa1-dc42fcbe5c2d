package com.workly.app.data

import android.content.Context
import androidx.room.RoomDatabase
import androidx.sqlite.db.SupportSQLiteDatabase
import com.workly.app.data.database.WorklyDatabase
import com.workly.app.data.repository.*
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class DatabaseInitializer @Inject constructor(
    @ApplicationContext private val context: Context
) {

    fun initializeDatabase(
        shiftRepository: ShiftRepository,
        attendanceRepository: AttendanceRepository,
        noteRepository: NoteRepository,
        settingsRepository: SettingsRepository,
        dailyWorkStatusRepository: DailyWorkStatusRepository
    ) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                // Check if database is already populated
                val shiftsCount = shiftRepository.getShiftsCount()
                if (shiftsCount == 0) {
                    populateInitialData(
                        shiftRepository,
                        attendanceRepository,
                        noteRepository,
                        settingsRepository,
                        dailyWorkStatusRepository
                    )
                }
            } catch (e: Exception) {
                // Log error but don't crash the app
                e.printStackTrace()
            }
        }
    }

    private suspend fun populateInitialData(
        shiftRepository: ShiftRepository,
        attendanceRepository: AttendanceRepository,
        noteRepository: NoteRepository,
        settingsRepository: SettingsRepository,
        dailyWorkStatusRepository: DailyWorkStatusRepository
    ) {
        // Insert sample shifts
        val sampleShifts = SampleDataProvider.getSampleShifts()
        shiftRepository.insertShifts(sampleShifts)

        // Insert sample attendance logs
        val sampleLogs = SampleDataProvider.getSampleAttendanceLogs()
        attendanceRepository.insertAttendanceLogs(sampleLogs)

        // Insert sample notes
        val sampleNotes = SampleDataProvider.getSampleNotes()
        noteRepository.insertNotes(sampleNotes)

        // Insert sample daily work status
        val sampleDailyStatus = SampleDataProvider.getSampleDailyWorkStatus()
        dailyWorkStatusRepository.insertDailyWorkStatuses(sampleDailyStatus)

        // Initialize user settings
        val sampleSettings = SampleDataProvider.getSampleUserSettings()
        settingsRepository.updateUserSettings(sampleSettings)
    }

    companion object {
        fun createCallback(initializer: DatabaseInitializer): RoomDatabase.Callback {
            return object : RoomDatabase.Callback() {
                override fun onCreate(db: SupportSQLiteDatabase) {
                    super.onCreate(db)
                    // Database initialization will be handled by the initializer
                }
            }
        }
    }
}
