package com.workly.app.data.database.dao

import androidx.room.*
import com.workly.app.data.model.Note
import kotlinx.coroutines.flow.Flow
import java.time.LocalDateTime

@Dao
interface NoteDao {
    
    @Query("SELECT * FROM notes ORDER BY isPriority DESC, createdAt DESC")
    fun getAllNotes(): Flow<List<Note>>
    
    @Query("SELECT * FROM notes WHERE id = :id")
    suspend fun getNoteById(id: String): Note?
    
    @Query("SELECT * FROM notes WHERE id = :id")
    fun getNoteByIdFlow(id: String): Flow<Note?>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertNote(note: Note)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertNotes(notes: List<Note>)
    
    @Update
    suspend fun updateNote(note: Note)
    
    @Delete
    suspend fun deleteNote(note: Note)
    
    @Query("DELETE FROM notes WHERE id = :id")
    suspend fun deleteNoteById(id: String)
    
    @Query("DELETE FROM notes")
    suspend fun deleteAllNotes()
    
    // Get active notes (not hidden and not snoozed)
    @Query("""
        SELECT * FROM notes 
        WHERE isHiddenFromHome = 0 
        AND (snoozeUntil IS NULL OR snoozeUntil <= :currentTime)
        ORDER BY isPriority DESC, createdAt DESC
    """)
    suspend fun getActiveNotes(currentTime: LocalDateTime = LocalDateTime.now()): List<Note>
    
    @Query("""
        SELECT * FROM notes 
        WHERE isHiddenFromHome = 0 
        AND (snoozeUntil IS NULL OR snoozeUntil <= :currentTime)
        ORDER BY isPriority DESC, createdAt DESC
    """)
    fun getActiveNotesFlow(currentTime: LocalDateTime = LocalDateTime.now()): Flow<List<Note>>
    
    // Get priority notes
    @Query("SELECT * FROM notes WHERE isPriority = 1 ORDER BY createdAt DESC")
    suspend fun getPriorityNotes(): List<Note>
    
    @Query("SELECT * FROM notes WHERE isPriority = 1 ORDER BY createdAt DESC")
    fun getPriorityNotesFlow(): Flow<List<Note>>
    
    // Get notes with reminders
    @Query("SELECT * FROM notes WHERE reminderDateTime IS NOT NULL AND enableNotifications = 1 ORDER BY reminderDateTime ASC")
    suspend fun getNotesWithReminders(): List<Note>
    
    @Query("SELECT * FROM notes WHERE reminderDateTime IS NOT NULL AND enableNotifications = 1 ORDER BY reminderDateTime ASC")
    fun getNotesWithRemindersFlow(): Flow<List<Note>>
    
    // Get overdue reminders
    @Query("""
        SELECT * FROM notes 
        WHERE reminderDateTime IS NOT NULL 
        AND reminderDateTime <= :currentTime 
        AND enableNotifications = 1
        ORDER BY reminderDateTime ASC
    """)
    suspend fun getOverdueReminders(currentTime: LocalDateTime = LocalDateTime.now()): List<Note>
    
    // Get notes associated with shift
    @Query("SELECT * FROM notes WHERE associatedShiftIds LIKE '%' || :shiftId || '%' ORDER BY isPriority DESC, createdAt DESC")
    suspend fun getNotesForShift(shiftId: String): List<Note>
    
    @Query("SELECT * FROM notes WHERE associatedShiftIds LIKE '%' || :shiftId || '%' ORDER BY isPriority DESC, createdAt DESC")
    fun getNotesForShiftFlow(shiftId: String): Flow<List<Note>>
    
    // Search notes
    @Query("""
        SELECT * FROM notes 
        WHERE title LIKE '%' || :query || '%' 
        OR content LIKE '%' || :query || '%'
        ORDER BY isPriority DESC, createdAt DESC
    """)
    suspend fun searchNotes(query: String): List<Note>
    
    @Query("""
        SELECT * FROM notes 
        WHERE title LIKE '%' || :query || '%' 
        OR content LIKE '%' || :query || '%'
        ORDER BY isPriority DESC, createdAt DESC
    """)
    fun searchNotesFlow(query: String): Flow<List<Note>>
    
    // Get notes count
    @Query("SELECT COUNT(*) FROM notes")
    suspend fun getNotesCount(): Int
    
    // Get active notes count
    @Query("""
        SELECT COUNT(*) FROM notes 
        WHERE isHiddenFromHome = 0 
        AND (snoozeUntil IS NULL OR snoozeUntil <= :currentTime)
    """)
    suspend fun getActiveNotesCount(currentTime: LocalDateTime = LocalDateTime.now()): Int
    
    // Get priority notes count
    @Query("SELECT COUNT(*) FROM notes WHERE isPriority = 1")
    suspend fun getPriorityNotesCount(): Int
}
