package com.workly.app.ui.home

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.workly.app.databinding.FragmentHomeBinding
import com.workly.app.ui.home.adapter.AttendanceHistoryAdapter
import com.workly.app.ui.home.adapter.NotesAdapter
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

@AndroidEntryPoint
class HomeFragment : Fragment() {

    private var _binding: FragmentHomeBinding? = null
    private val binding get() = _binding!!

    private val viewModel: HomeViewModel by viewModels()
    
    private lateinit var attendanceHistoryAdapter: AttendanceHistoryAdapter
    private lateinit var notesAdapter: Notes<PERSON>dapter

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentHomeBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupRecyclerViews()
        setupClickListeners()
        observeViewModel()
    }

    private fun setupRecyclerViews() {
        // Setup attendance history RecyclerView
        attendanceHistoryAdapter = AttendanceHistoryAdapter { attendanceLog ->
            // Handle attendance log click
        }
        binding.recyclerViewAttendanceHistory.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = attendanceHistoryAdapter
        }

        // Setup notes RecyclerView
        notesAdapter = NotesAdapter(
            onNoteClick = { note ->
                // Navigate to note detail
            },
            onNoteSnooze = { note ->
                viewModel.snoozeNote(note)
            }
        )
        binding.recyclerViewNotes.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = notesAdapter
        }
    }

    private fun setupClickListeners() {
        binding.buttonMultiFunction.setOnClickListener {
            viewModel.handleMultiFunctionButtonClick()
        }

        binding.cardWeather.setOnClickListener {
            // Navigate to weather detail
        }
    }

    private fun observeViewModel() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.uiState.collect { state ->
                updateUI(state)
            }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.attendanceHistory.collect { logs ->
                attendanceHistoryAdapter.submitList(logs)
            }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.activeNotes.collect { notes ->
                notesAdapter.submitList(notes)
            }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.weatherData.collect { weather ->
                updateWeatherUI(weather)
            }
        }
    }

    private fun updateUI(state: HomeUiState) {
        binding.apply {
            // Update multi-function button
            buttonMultiFunction.text = state.buttonText
            buttonMultiFunction.isEnabled = state.buttonEnabled
            
            // Update status display
            textViewCurrentStatus.text = state.currentStatus
            
            // Update loading states
            progressBarLoading.visibility = if (state.isLoading) View.VISIBLE else View.GONE
        }
    }

    private fun updateWeatherUI(weather: com.workly.app.data.model.WeatherData?) {
        weather?.let {
            binding.apply {
                textViewWeatherTemp.text = it.current.getTemperatureDisplay()
                textViewWeatherDescription.text = it.current.description
                textViewWeatherLocation.text = it.current.location
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
