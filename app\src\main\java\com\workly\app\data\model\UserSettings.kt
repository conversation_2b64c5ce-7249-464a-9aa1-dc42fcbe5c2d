package com.workly.app.data.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class UserSettings(
    val language: String = "vi",
    val theme: Theme = Theme.LIGHT,
    val multiButtonMode: MultiButtonMode = MultiButtonMode.AUTO,
    val alarmSoundEnabled: Boolean = true,
    val alarmVibrationEnabled: Boolean = true,
    val weatherWarningEnabled: Boolean = true,
    val weatherLocation: WeatherLocation? = null,
    val changeShiftReminderMode: ChangeShiftReminderMode = ChangeShiftReminderMode.ASK_WEEKLY,
    val timeFormat: TimeFormat = TimeFormat.TWENTY_FOUR_HOUR,
    val firstDayOfWeek: FirstDayOfWeek = FirstDayOfWeek.MONDAY,
    
    // Location settings
    val homeLocation: SavedLocation? = null,
    val workLocation: SavedLocation? = null,
    val autoCheckInEnabled: Boolean = false,
    val autoCheckInRadius: Int = 100, // <PERSON>án kính tự động chấm công (mét)
    val locationTrackingEnabled: Boolean = false,
    val lateThresholdMinutes: Int = 15,
    val rapidPressThresholdSeconds: Int = 30, // Ngưỡng phát hiện "Bấm Nhanh" (giây)
    val overtimeRates: OvertimeRates = OvertimeRates(),
    val notesDisplayCount: Int = 3,
    val notesTimeWindow: NotesTimeWindow = NotesTimeWindow.ALWAYS, // minutes or 'always'
    val notesShowConflictWarning: Boolean = true,
    val rotationConfig: RotationConfig? = null
) : Parcelable

@Parcelize
data class SavedLocation(
    val id: String,
    val name: String,
    val address: String,
    val latitude: Double,
    val longitude: Double,
    val radius: Int, // Bán kính tính bằng mét để xác định "gần"
    val createdAt: String,
    val updatedAt: String
) : Parcelable

@Parcelize
data class WeatherLocation(
    val home: LocationCoordinate? = null,
    val work: LocationCoordinate? = null,
    val useSingleLocation: Boolean = false
) : Parcelable

@Parcelize
data class LocationCoordinate(
    val lat: Double,
    val lon: Double
) : Parcelable

@Parcelize
data class OvertimeRates(
    val weekday: Double = 1.5,
    val saturday: Double = 2.0,
    val sunday: Double = 2.0,
    val holiday: Double = 3.0
) : Parcelable

@Parcelize
data class RotationConfig(
    val rotationShifts: List<String> = emptyList(),
    val rotationFrequency: RotationFrequency = RotationFrequency.WEEKLY,
    val rotationLastAppliedDate: String? = null,
    val currentRotationIndex: Int = 0
) : Parcelable

enum class Theme {
    LIGHT, DARK
}

enum class MultiButtonMode {
    FULL, SIMPLE, AUTO
}

enum class ChangeShiftReminderMode {
    ASK_WEEKLY, ROTATE, DISABLED
}

enum class TimeFormat {
    TWELVE_HOUR, TWENTY_FOUR_HOUR
}

enum class FirstDayOfWeek {
    MONDAY, SUNDAY
}

enum class NotesTimeWindow(val minutes: Int?) {
    FIVE_MINUTES(5),
    FIFTEEN_MINUTES(15),
    THIRTY_MINUTES(30),
    SIXTY_MINUTES(60),
    ALWAYS(null)
}

enum class RotationFrequency {
    WEEKLY, BIWEEKLY, TRIWEEKLY, MONTHLY
}
