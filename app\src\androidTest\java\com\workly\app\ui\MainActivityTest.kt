package com.workly.app.ui

import androidx.test.espresso.Espresso.onView
import androidx.test.espresso.assertion.ViewAssertions.matches
import androidx.test.espresso.matcher.ViewMatchers.*
import androidx.test.ext.junit.rules.ActivityScenarioRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.workly.app.R
import dagger.hilt.android.testing.HiltAndroidRule
import dagger.hilt.android.testing.HiltAndroidTest
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

@HiltAndroidTest
@RunWith(AndroidJUnit4::class)
class MainActivityTest {

    @get:Rule
    var hiltRule = HiltAndroidRule(this)

    @get:Rule
    var activityRule = ActivityScenarioRule(MainActivity::class.java)

    @Before
    fun setup() {
        hiltRule.inject()
    }

    @Test
    fun mainActivity_displaysBottomNavigation() {
        // Check if bottom navigation is displayed
        onView(withId(R.id.nav_view))
            .check(matches(isDisplayed()))
    }

    @Test
    fun mainActivity_displaysNavHostFragment() {
        // Check if nav host fragment is displayed
        onView(withId(R.id.nav_host_fragment_activity_main))
            .check(matches(isDisplayed()))
    }

    @Test
    fun bottomNavigation_hasCorrectMenuItems() {
        // Check if all menu items are present
        onView(withText("Trang chủ")).check(matches(isDisplayed()))
        onView(withText("Ca làm việc")).check(matches(isDisplayed()))
        onView(withText("Ghi chú")).check(matches(isDisplayed()))
        onView(withText("Thống kê")).check(matches(isDisplayed()))
        onView(withText("Cài đặt")).check(matches(isDisplayed()))
    }
}
