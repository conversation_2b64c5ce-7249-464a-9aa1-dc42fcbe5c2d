<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="4dp"
    android:layout_marginVertical="2dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="12dp">

        <!-- Priority Indicator -->
        <View
            android:id="@+id/view_priority_indicator"
            android:layout_width="4dp"
            android:layout_height="0dp"
            android:background="@color/orange_500"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

        <!-- Title -->
        <TextView
            android:id="@+id/text_view_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="8dp"
            android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
            android:textColor="?attr/colorOnSurface"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@id/button_snooze"
            app:layout_constraintStart_toEndOf="@id/view_priority_indicator"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="⭐ Ghi chú quan trọng" />

        <!-- Content -->
        <TextView
            android:id="@+id/text_view_content"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="8dp"
            android:ellipsize="end"
            android:maxLines="2"
            android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
            android:textColor="?attr/colorOnSurfaceVariant"
            app:layout_constraintEnd_toStartOf="@id/button_snooze"
            app:layout_constraintStart_toEndOf="@id/view_priority_indicator"
            app:layout_constraintTop_toBottomOf="@id/text_view_title"
            tools:text="Nội dung ghi chú chi tiết sẽ được hiển thị ở đây với tối đa 2 dòng" />

        <!-- Date -->
        <TextView
            android:id="@+id/text_view_date"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="8dp"
            android:textAppearance="@style/TextAppearance.Material3.BodySmall"
            android:textColor="?attr/colorOnSurfaceVariant"
            app:layout_constraintEnd_toStartOf="@id/button_snooze"
            app:layout_constraintStart_toEndOf="@id/view_priority_indicator"
            app:layout_constraintTop_toBottomOf="@id/text_view_content"
            tools:text="02/07/2025 08:30" />

        <!-- Reminder -->
        <TextView
            android:id="@+id/text_view_reminder"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginTop="2dp"
            android:layout_marginEnd="8dp"
            android:drawablePadding="4dp"
            android:textAppearance="@style/TextAppearance.Material3.BodySmall"
            android:textColor="?attr/colorOnSurfaceVariant"
            android:visibility="gone"
            app:drawableStartCompat="@drawable/ic_alarm_16"
            app:layout_constraintEnd_toStartOf="@id/button_snooze"
            app:layout_constraintStart_toEndOf="@id/view_priority_indicator"
            app:layout_constraintTop_toBottomOf="@id/text_view_date"
            tools:text="Nhắc nhở: 02/07/2025 09:00"
            tools:visibility="visible" />

        <!-- Shift Count -->
        <TextView
            android:id="@+id/text_view_shift_count"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginTop="2dp"
            android:layout_marginEnd="8dp"
            android:drawablePadding="4dp"
            android:textAppearance="@style/TextAppearance.Material3.BodySmall"
            android:textColor="?attr/colorOnSurfaceVariant"
            android:visibility="gone"
            app:drawableStartCompat="@drawable/ic_schedule_16"
            app:layout_constraintEnd_toStartOf="@id/button_snooze"
            app:layout_constraintStart_toEndOf="@id/view_priority_indicator"
            app:layout_constraintTop_toBottomOf="@id/text_view_reminder"
            tools:text="2 ca làm việc"
            tools:visibility="visible" />

        <!-- Snooze Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/button_snooze"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minWidth="0dp"
            android:text="Hoãn"
            android:textSize="12sp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>
