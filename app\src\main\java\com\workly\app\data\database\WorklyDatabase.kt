package com.workly.app.data.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import android.content.Context
import com.workly.app.data.database.dao.AttendanceDao
import com.workly.app.data.database.dao.NoteDao
import com.workly.app.data.database.dao.ShiftDao
import com.workly.app.data.database.dao.DailyWorkStatusDao
import com.workly.app.data.model.AttendanceLog
import com.workly.app.data.model.DailyWorkStatus
import com.workly.app.data.model.Note
import com.workly.app.data.model.Shift

@Database(
    entities = [
        Shift::class,
        AttendanceLog::class,
        Note::class,
        DailyWorkStatus::class
    ],
    version = 1,
    exportSchema = true
)
@TypeConverters(Converters::class)
abstract class WorklyDatabase : RoomDatabase() {
    
    abstract fun shiftDao(): ShiftDao
    abstract fun attendanceDao(): AttendanceDao
    abstract fun noteDao(): NoteDao
    abstract fun dailyWorkStatusDao(): DailyWorkStatusDao

    companion object {
        const val DATABASE_NAME = "workly_database"
        
        @Volatile
        private var INSTANCE: WorklyDatabase? = null

        fun getDatabase(context: Context): WorklyDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    WorklyDatabase::class.java,
                    DATABASE_NAME
                )
                    .fallbackToDestructiveMigration()
                    .build()
                INSTANCE = instance
                instance
            }
        }
    }
}
