<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mobile_navigation"
    app:startDestination="@+id/navigation_home">

    <fragment
        android:id="@+id/navigation_home"
        android:name="com.workly.app.ui.home.HomeFragment"
        android:label="@string/title_home"
        tools:layout="@layout/fragment_home" />

    <fragment
        android:id="@+id/navigation_shifts"
        android:name="com.workly.app.ui.shifts.ShiftsFragment"
        android:label="@string/title_shifts"
        tools:layout="@layout/fragment_shifts" />

    <fragment
        android:id="@+id/navigation_notes"
        android:name="com.workly.app.ui.notes.NotesFragment"
        android:label="@string/title_notes"
        tools:layout="@layout/fragment_notes" />

    <fragment
        android:id="@+id/navigation_statistics"
        android:name="com.workly.app.ui.statistics.StatisticsFragment"
        android:label="@string/title_statistics"
        tools:layout="@layout/fragment_statistics" />

    <fragment
        android:id="@+id/navigation_settings"
        android:name="com.workly.app.ui.settings.SettingsFragment"
        android:label="@string/title_settings"
        tools:layout="@layout/fragment_settings" />

</navigation>
