package com.workly.app.ui.shifts

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import com.workly.app.databinding.FragmentShiftsBinding
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class ShiftsFragment : Fragment() {

    private var _binding: FragmentShiftsBinding? = null
    private val binding get() = _binding!!

    private val viewModel: ShiftsViewModel by viewModels()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentShiftsBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupUI()
        observeViewModel()
    }

    private fun setupUI() {
        binding.textShifts.text = "Quản lý ca làm việc"
    }

    private fun observeViewModel() {
        // TODO: Implement ViewModel observations
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
