# Dữ liệu mẫu cho ứng dụng Workly

## 📋 Tổng quan

Ứng dụng Workly đã được cấu hình với **dữ liệu mẫu đầy đủ** để bạn có thể chạy và test ngay lập tức mà không cần tạo dữ liệu thủ công.

## 🗂️ Các loại dữ liệu mẫu

### 1. **Ca làm việc (Shifts)** - 3 ca mẫu

#### Ca hành chính
- **Tên**: Ca hành chính
- **Giờ**: 08:00 - 17:00
- **Ngày**: <PERSON><PERSON><PERSON> 2 - <PERSON><PERSON><PERSON> 6
- **Nhắc nhở**: Trước 30 phút, sau 15 phút
- **Giờ nghỉ**: 60 phút

#### Ca tối
- **Tên**: Ca tối  
- **Giờ**: 18:00 - 02:00
- **Ngày**: <PERSON><PERSON><PERSON> 2 - Th<PERSON> 6
- **Nhắc nhở**: Tr<PERSON>ớ<PERSON> 45 phút, sau 20 phút
- **G<PERSON><PERSON> nghỉ**: 30 phút
- **Loại**: Ca đêm

#### Ca cuối tuần
- **Tên**: Ca cuối tuần
- **Giờ**: 09:00 - 15:00
- **Ngày**: Thứ 7 - Chủ nhật
- **Nhắc nhở**: Trước 60 phút, sau 10 phút
- **Giờ nghỉ**: 45 phút

### 2. **Lịch sử chấm công (Attendance Logs)** - 5 bản ghi

- ✅ **Đi làm** - 2 giờ trước (thủ công)
- ✅ **Vào làm** - 1.5 giờ trước (tự động)
- ✅ **Ra về** - Hôm qua 17:15 (thủ công)
- ✅ **Hoàn thành** - Hôm qua 18:00 (tự động)
- ✅ **Đi làm sớm** - 2 ngày trước 7:45 (thủ công)

### 3. **Ghi chú (Notes)** - 5 ghi chú

#### Ghi chú ưu tiên
- ⭐ **Họp team hàng tuần** - Nhắc nhở: Ngày mai 8:30
- ⭐ **Nộp báo cáo tháng** - Nhắc nhở: 3 ngày nữa 14:00

#### Ghi chú thường
- 📝 **Kiểm tra sức khỏe định kỳ** - Nhắc nhở: 7 ngày nữa 10:00
- 📝 **Cập nhật CV và LinkedIn** - Không nhắc nhở
- 📝 **Học khóa học online** - Nhắc nhở: 2 ngày nữa 20:00

### 4. **Trạng thái công việc hàng ngày (Daily Work Status)** - 5 ngày

- **Hôm nay**: Đã đi, chưa vào (DA_DI_CHUA_VAO)
- **Hôm qua**: Đủ công (DU_CONG) - 8.25 giờ
- **2 ngày trước**: Đi muộn (DI_MUON) - Muộn 15 phút
- **3 ngày trước**: Về sớm (VE_SOM) - Sớm 30 phút  
- **4 ngày trước**: Nghỉ phép (NGHI_PHEP)

### 5. **Cài đặt người dùng (User Settings)**

#### Cài đặt cơ bản
- **Ngôn ngữ**: Tiếng Việt
- **Giao diện**: Sáng (Light theme)
- **Định dạng giờ**: 24 giờ
- **Ngày đầu tuần**: Thứ Hai

#### Vị trí
- **Nhà**: 123 Đường ABC, Cầu Giấy, Hà Nội (21.0285, 105.8542)
- **Công ty**: 456 Đường XYZ, Ba Đình, Hà Nội (21.0245, 105.8412)
- **Chấm công tự động**: Bật (bán kính 100m)

#### Thông báo
- **Âm thanh**: Bật
- **Rung**: Bật
- **Cảnh báo thời tiết**: Bật

### 6. **Dữ liệu thời tiết (Weather Data)**

#### Thời tiết hiện tại
- **Nhiệt độ**: 28.5°C
- **Mô tả**: Nắng ít mây
- **Vị trí**: Hà Nội

#### Dự báo (4 mốc thời gian)
- **+3 giờ**: 30°C - Nắng
- **+6 giờ**: 32°C - Nắng nóng
- **+9 giờ**: 29°C - Có mây
- **+12 giờ**: 26°C - Mưa nhẹ

#### Cảnh báo thời tiết
- 🌡️ **Nắng nóng**: 32°C buổi chiều tại công ty
- 🌧️ **Mưa**: Buổi tối tại nhà

## 🔄 Cách dữ liệu được tải

### Tự động khởi tạo
1. **Khi lần đầu chạy app**: `DatabaseInitializer` sẽ kiểm tra database
2. **Nếu trống**: Tự động populate tất cả dữ liệu mẫu
3. **Nếu có dữ liệu**: Không làm gì (giữ nguyên dữ liệu người dùng)

### Thứ tự khởi tạo
1. **Shifts** → Ca làm việc
2. **Attendance Logs** → Lịch sử chấm công
3. **Notes** → Ghi chú
4. **Daily Work Status** → Trạng thái hàng ngày
5. **User Settings** → Cài đặt người dùng

## 📱 Trải nghiệm với dữ liệu mẫu

### Tab Home
- Hiển thị **trạng thái hiện tại**: "Đã đi, chưa vào"
- **Lịch sử chấm công**: 5 bản ghi gần nhất
- **Ghi chú**: 3 ghi chú ưu tiên và có nhắc nhở
- **Thời tiết**: Hà Nội 28.5°C với cảnh báo

### Tab Shifts
- Hiển thị **3 ca làm việc** với thông tin chi tiết
- Thời gian, ngày áp dụng, cài đặt nhắc nhở

### Tab Notes  
- Hiển thị **5 ghi chú** với ưu tiên và nhắc nhở
- Phân biệt ghi chú quan trọng (⭐) và thường

### Tab Statistics
- Dữ liệu thống kê từ **Daily Work Status**
- Tổng giờ làm, overtime, đi muộn, về sớm

### Tab Settings
- **Cài đặt đầy đủ** với vị trí nhà/công ty
- Tùy chọn thông báo, giao diện, ngôn ngữ

## 🛠️ Tùy chỉnh dữ liệu mẫu

### Thay đổi dữ liệu
Chỉnh sửa file `SampleDataProvider.kt` để:
- Thêm/bớt ca làm việc
- Thay đổi vị trí nhà/công ty
- Cập nhật ghi chú và nhắc nhở
- Điều chỉnh lịch sử chấm công

### Reset dữ liệu
Để reset về dữ liệu mẫu ban đầu:
1. **Xóa app** khỏi device
2. **Cài đặt lại** → Dữ liệu mẫu sẽ được tạo lại

### Tắt dữ liệu mẫu
Để tắt auto-populate dữ liệu mẫu:
1. Comment code trong `DatabaseInitializer.initializeDatabase()`
2. Rebuild app

## 📊 Thống kê dữ liệu mẫu

- **Shifts**: 3 ca làm việc
- **Attendance Logs**: 5 bản ghi
- **Notes**: 5 ghi chú (2 ưu tiên, 3 thường)
- **Daily Status**: 5 ngày (5 trạng thái khác nhau)
- **Settings**: Đầy đủ cấu hình
- **Weather**: Dữ liệu thời tiết Hà Nội

## ✅ Lợi ích của dữ liệu mẫu

1. **Test ngay lập tức** - Không cần tạo dữ liệu thủ công
2. **Demo đầy đủ** - Thể hiện tất cả tính năng
3. **UX tốt** - Người dùng thấy app "có nội dung"
4. **Development** - Dễ test các tính năng
5. **Realistic** - Dữ liệu giống thực tế

---

**Với dữ liệu mẫu này, ứng dụng Workly sẵn sàng để demo và sử dụng ngay! 🚀**
