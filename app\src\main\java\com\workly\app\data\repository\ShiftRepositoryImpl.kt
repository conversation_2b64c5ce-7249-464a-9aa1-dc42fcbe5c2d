package com.workly.app.data.repository

import com.workly.app.data.database.dao.ShiftDao
import com.workly.app.data.model.Shift
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ShiftRepositoryImpl @Inject constructor(
    private val shiftDao: ShiftDao
) : ShiftRepository {

    override fun getAllShifts(): Flow<List<Shift>> {
        return shiftDao.getAllShifts()
    }

    override suspend fun getShiftById(id: String): Shift? {
        return shiftDao.getShiftById(id)
    }

    override fun getShiftByIdFlow(id: String): Flow<Shift?> {
        return shiftDao.getShiftByIdFlow(id)
    }

    override suspend fun insertShift(shift: Shift) {
        shiftDao.insertShift(shift)
    }

    override suspend fun insertShifts(shifts: List<Shift>) {
        shiftDao.insertShifts(shifts)
    }

    override suspend fun updateShift(shift: Shift) {
        shiftDao.updateShift(shift)
    }

    override suspend fun deleteShift(shift: Shift) {
        shiftDao.deleteShift(shift)
    }

    override suspend fun deleteShiftById(id: String) {
        shiftDao.deleteShiftById(id)
    }

    override suspend fun deleteAllShifts() {
        shiftDao.deleteAllShifts()
    }

    override suspend fun getShiftsForDay(dayOfWeek: String): List<Shift> {
        return shiftDao.getShiftsForDay(dayOfWeek)
    }

    override fun getActiveShifts(): Flow<List<Shift>> {
        return shiftDao.getActiveShifts()
    }

    override fun searchShifts(query: String): Flow<List<Shift>> {
        return shiftDao.searchShifts(query)
    }

    override suspend fun getShiftsCount(): Int {
        return shiftDao.getShiftsCount()
    }
}
