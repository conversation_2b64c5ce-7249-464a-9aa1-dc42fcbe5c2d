// Generated by view binder compiler. Do not edit!
package com.workly.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.workly.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemNoteBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final MaterialButton buttonSnooze;

  @NonNull
  public final TextView textViewContent;

  @NonNull
  public final TextView textViewDate;

  @NonNull
  public final TextView textViewReminder;

  @NonNull
  public final TextView textViewShiftCount;

  @NonNull
  public final TextView textViewTitle;

  @NonNull
  public final View viewPriorityIndicator;

  private ItemNoteBinding(@NonNull MaterialCardView rootView, @NonNull MaterialButton buttonSnooze,
      @NonNull TextView textViewContent, @NonNull TextView textViewDate,
      @NonNull TextView textViewReminder, @NonNull TextView textViewShiftCount,
      @NonNull TextView textViewTitle, @NonNull View viewPriorityIndicator) {
    this.rootView = rootView;
    this.buttonSnooze = buttonSnooze;
    this.textViewContent = textViewContent;
    this.textViewDate = textViewDate;
    this.textViewReminder = textViewReminder;
    this.textViewShiftCount = textViewShiftCount;
    this.textViewTitle = textViewTitle;
    this.viewPriorityIndicator = viewPriorityIndicator;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemNoteBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemNoteBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_note, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemNoteBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button_snooze;
      MaterialButton buttonSnooze = ViewBindings.findChildViewById(rootView, id);
      if (buttonSnooze == null) {
        break missingId;
      }

      id = R.id.text_view_content;
      TextView textViewContent = ViewBindings.findChildViewById(rootView, id);
      if (textViewContent == null) {
        break missingId;
      }

      id = R.id.text_view_date;
      TextView textViewDate = ViewBindings.findChildViewById(rootView, id);
      if (textViewDate == null) {
        break missingId;
      }

      id = R.id.text_view_reminder;
      TextView textViewReminder = ViewBindings.findChildViewById(rootView, id);
      if (textViewReminder == null) {
        break missingId;
      }

      id = R.id.text_view_shift_count;
      TextView textViewShiftCount = ViewBindings.findChildViewById(rootView, id);
      if (textViewShiftCount == null) {
        break missingId;
      }

      id = R.id.text_view_title;
      TextView textViewTitle = ViewBindings.findChildViewById(rootView, id);
      if (textViewTitle == null) {
        break missingId;
      }

      id = R.id.view_priority_indicator;
      View viewPriorityIndicator = ViewBindings.findChildViewById(rootView, id);
      if (viewPriorityIndicator == null) {
        break missingId;
      }

      return new ItemNoteBinding((MaterialCardView) rootView, buttonSnooze, textViewContent,
          textViewDate, textViewReminder, textViewShiftCount, textViewTitle, viewPriorityIndicator);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
