<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="G:\IT\PROJECT-IDEAL\workly_android\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="G:\IT\PROJECT-IDEAL\workly_android\app\src\main\res"><file name="bg_chip_outline" path="G:\IT\PROJECT-IDEAL\workly_android\app\src\main\res\drawable\bg_chip_outline.xml" qualifiers="" type="drawable"/><file name="ic_alarm_16" path="G:\IT\PROJECT-IDEAL\workly_android\app\src\main\res\drawable\ic_alarm_16.xml" qualifiers="" type="drawable"/><file name="ic_analytics_24" path="G:\IT\PROJECT-IDEAL\workly_android\app\src\main\res\drawable\ic_analytics_24.xml" qualifiers="" type="drawable"/><file name="ic_home_24" path="G:\IT\PROJECT-IDEAL\workly_android\app\src\main\res\drawable\ic_home_24.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="G:\IT\PROJECT-IDEAL\workly_android\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="G:\IT\PROJECT-IDEAL\workly_android\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_location_on_16" path="G:\IT\PROJECT-IDEAL\workly_android\app\src\main\res\drawable\ic_location_on_16.xml" qualifiers="" type="drawable"/><file name="ic_note_16" path="G:\IT\PROJECT-IDEAL\workly_android\app\src\main\res\drawable\ic_note_16.xml" qualifiers="" type="drawable"/><file name="ic_note_24" path="G:\IT\PROJECT-IDEAL\workly_android\app\src\main\res\drawable\ic_note_24.xml" qualifiers="" type="drawable"/><file name="ic_notification" path="G:\IT\PROJECT-IDEAL\workly_android\app\src\main\res\drawable\ic_notification.xml" qualifiers="" type="drawable"/><file name="ic_schedule_16" path="G:\IT\PROJECT-IDEAL\workly_android\app\src\main\res\drawable\ic_schedule_16.xml" qualifiers="" type="drawable"/><file name="ic_schedule_24" path="G:\IT\PROJECT-IDEAL\workly_android\app\src\main\res\drawable\ic_schedule_24.xml" qualifiers="" type="drawable"/><file name="ic_settings_24" path="G:\IT\PROJECT-IDEAL\workly_android\app\src\main\res\drawable\ic_settings_24.xml" qualifiers="" type="drawable"/><file name="ic_sunny" path="G:\IT\PROJECT-IDEAL\workly_android\app\src\main\res\drawable\ic_sunny.xml" qualifiers="" type="drawable"/><file name="activity_main" path="G:\IT\PROJECT-IDEAL\workly_android\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="fragment_home" path="G:\IT\PROJECT-IDEAL\workly_android\app\src\main\res\layout\fragment_home.xml" qualifiers="" type="layout"/><file name="fragment_notes" path="G:\IT\PROJECT-IDEAL\workly_android\app\src\main\res\layout\fragment_notes.xml" qualifiers="" type="layout"/><file name="fragment_settings" path="G:\IT\PROJECT-IDEAL\workly_android\app\src\main\res\layout\fragment_settings.xml" qualifiers="" type="layout"/><file name="fragment_shifts" path="G:\IT\PROJECT-IDEAL\workly_android\app\src\main\res\layout\fragment_shifts.xml" qualifiers="" type="layout"/><file name="fragment_statistics" path="G:\IT\PROJECT-IDEAL\workly_android\app\src\main\res\layout\fragment_statistics.xml" qualifiers="" type="layout"/><file name="item_attendance_log" path="G:\IT\PROJECT-IDEAL\workly_android\app\src\main\res\layout\item_attendance_log.xml" qualifiers="" type="layout"/><file name="item_note" path="G:\IT\PROJECT-IDEAL\workly_android\app\src\main\res\layout\item_note.xml" qualifiers="" type="layout"/><file name="bottom_nav_menu" path="G:\IT\PROJECT-IDEAL\workly_android\app\src\main\res\menu\bottom_nav_menu.xml" qualifiers="" type="menu"/><file name="ic_launcher" path="G:\IT\PROJECT-IDEAL\workly_android\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="G:\IT\PROJECT-IDEAL\workly_android\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="G:\IT\PROJECT-IDEAL\workly_android\app\src\main\res\mipmap-hdpi\ic_launcher.xml" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="G:\IT\PROJECT-IDEAL\workly_android\app\src\main\res\mipmap-hdpi\ic_launcher_round.xml" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="G:\IT\PROJECT-IDEAL\workly_android\app\src\main\res\mipmap-mdpi\ic_launcher.xml" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="G:\IT\PROJECT-IDEAL\workly_android\app\src\main\res\mipmap-mdpi\ic_launcher_round.xml" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="G:\IT\PROJECT-IDEAL\workly_android\app\src\main\res\mipmap-xhdpi\ic_launcher.xml" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="G:\IT\PROJECT-IDEAL\workly_android\app\src\main\res\mipmap-xhdpi\ic_launcher_round.xml" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="G:\IT\PROJECT-IDEAL\workly_android\app\src\main\res\mipmap-xxhdpi\ic_launcher.xml" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="G:\IT\PROJECT-IDEAL\workly_android\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.xml" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="G:\IT\PROJECT-IDEAL\workly_android\app\src\main\res\mipmap-xxxhdpi\ic_launcher.xml" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="G:\IT\PROJECT-IDEAL\workly_android\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.xml" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="mobile_navigation" path="G:\IT\PROJECT-IDEAL\workly_android\app\src\main\res\navigation\mobile_navigation.xml" qualifiers="" type="navigation"/><file path="G:\IT\PROJECT-IDEAL\workly_android\app\src\main\res\values\colors.xml" qualifiers=""><color name="blue_50">#E3F2FD</color><color name="blue_100">#BBDEFB</color><color name="blue_200">#90CAF9</color><color name="blue_300">#64B5F6</color><color name="blue_400">#42A5F5</color><color name="blue_500">#2196F3</color><color name="blue_600">#1E88E5</color><color name="blue_700">#1976D2</color><color name="blue_800">#1565C0</color><color name="blue_900">#0D47A1</color><color name="white">#FFFFFF</color><color name="black">#000000</color><color name="gray_50">#FAFAFA</color><color name="gray_100">#F5F5F5</color><color name="gray_200">#EEEEEE</color><color name="gray_300">#E0E0E0</color><color name="gray_400">#BDBDBD</color><color name="gray_500">#9E9E9E</color><color name="gray_600">#757575</color><color name="gray_700">#616161</color><color name="gray_800">#424242</color><color name="gray_900">#212121</color><color name="green_500">#4CAF50</color><color name="red_500">#F44336</color><color name="orange_500">#FF9800</color><color name="yellow_500">#FFEB3B</color><color name="background_light">#FFFFFF</color><color name="background_dark">#121212</color><color name="surface_light">#FFFFFF</color><color name="surface_dark">#1E1E1E</color></file><file path="G:\IT\PROJECT-IDEAL\workly_android\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Workly</string><string name="title_home">Trang chủ</string><string name="title_shifts">Ca làm việc</string><string name="title_notes">Ghi chú</string><string name="title_statistics">Thống kê</string><string name="title_settings">Cài đặt</string><string name="ready_to_work">Sẵn sàng làm việc</string><string name="go_work">Đi làm</string><string name="check_in">Vào làm</string><string name="check_out">Ra về</string><string name="completed">Hoàn thành</string><string name="notes_title">Ghi chú</string><string name="attendance_history_title">Lịch sử chấm công</string><string name="weather_sunny">Nắng</string><string name="weather_cloudy">Có mây</string><string name="weather_rainy">Mưa</string><string name="weather_stormy">Bão</string><string name="attendance_go_work">Đi làm</string><string name="attendance_check_in">Vào làm</string><string name="attendance_punch">Chấm công</string><string name="attendance_check_out">Ra về</string><string name="attendance_complete">Hoàn thành</string><string name="status_completed">Hoàn thành</string><string name="status_late">Đi muộn</string><string name="status_early">Về sớm</string><string name="status_absent">Vắng mặt</string><string name="status_day_off">Nghỉ</string><string name="status_pending">Chờ xử lý</string><string name="loading">Đang tải...</string><string name="error">Lỗi</string><string name="retry">Thử lại</string><string name="cancel">Hủy</string><string name="ok">OK</string><string name="save">Lưu</string><string name="delete">Xóa</string><string name="edit">Sửa</string><string name="add">Thêm</string><string name="permission_location_title">Quyền truy cập vị trí</string><string name="permission_location_message">Ứng dụng cần quyền truy cập vị trí để xác định vị trí nhà và công ty cho tính năng cảnh báo thời tiết và chấm công tự động.</string><string name="permission_notification_title">Quyền thông báo</string><string name="permission_notification_message">Ứng dụng cần quyền gửi thông báo để nhắc nhở về ca làm việc và ghi chú.</string><string name="notification_shift_reminder_title">Nhắc nhở ca làm việc</string><string name="notification_note_reminder_title">Nhắc nhở ghi chú</string><string name="notification_location_service_title">Dịch vụ vị trí</string><string name="notification_location_service_content">Đang theo dõi vị trí cho chấm công tự động</string></file><file path="G:\IT\PROJECT-IDEAL\workly_android\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.Workly" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/blue_500</item>
        <item name="colorPrimaryVariant">@color/blue_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/blue_200</item>
        <item name="colorSecondaryVariant">@color/blue_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor" ns1:targetApi="l">?attr/colorPrimaryVariant</item>
        
    </style><style name="Theme.Workly.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style><style name="Theme.Workly.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar"/><style name="Theme.Workly.PopupOverlay" parent="ThemeOverlay.AppCompat.Light"/></file><file path="G:\IT\PROJECT-IDEAL\workly_android\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Theme.Workly" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/blue_200</item>
        <item name="colorPrimaryVariant">@color/blue_700</item>
        <item name="colorOnPrimary">@color/black</item>
        
        <item name="colorSecondary">@color/blue_200</item>
        <item name="colorSecondaryVariant">@color/blue_200</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor" ns1:targetApi="l">?attr/colorPrimaryVariant</item>
        
        <item name="android:colorBackground">@color/background_dark</item>
        <item name="colorSurface">@color/surface_dark</item>
        
    </style></file><file name="backup_rules" path="G:\IT\PROJECT-IDEAL\workly_android\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="G:\IT\PROJECT-IDEAL\workly_android\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="G:\IT\PROJECT-IDEAL\workly_android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="G:\IT\PROJECT-IDEAL\workly_android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="G:\IT\PROJECT-IDEAL\workly_android\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="G:\IT\PROJECT-IDEAL\workly_android\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>