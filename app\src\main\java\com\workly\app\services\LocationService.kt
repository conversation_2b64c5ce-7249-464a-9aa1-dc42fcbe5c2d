package com.workly.app.services

import android.location.Location
import com.workly.app.data.model.SavedLocation
import kotlinx.coroutines.flow.Flow

interface LocationService {
    suspend fun getCurrentLocation(): Location?
    suspend fun isLocationEnabled(): Boolean
    suspend fun requestLocationPermission(): Boolean
    suspend fun calculateDistance(location1: SavedLocation, location2: Location): Float
    suspend fun isWithinRadius(targetLocation: SavedLocation, currentLocation: Location): Boolean
    fun startLocationTracking()
    fun stopLocationTracking()
    fun getLocationUpdates(): Flow<Location>
    suspend fun geocodeAddress(address: String): SavedLocation?
    suspend fun reverseGeocode(latitude: Double, longitude: Double): String?
}
