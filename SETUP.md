# Hướng dẫn Setup dự án Workly Android

## Y<PERSON>u cầu hệ thống

### 1. Cài đặt Android Studio
- Tải và cài đặt **Android Studio Hedgehog** hoặc mới hơn từ: https://developer.android.com/studio
- Trong Android Studio, cài đặt:
  - Android SDK 34 (API level 34)
  - Android SDK Build-Tools 34.0.0
  - Android Emulator (nếu muốn test trên emulator)

### 2. Cài đặt JDK
- Cài đặt **JDK 17** hoặc mới hơn
- <PERSON><PERSON> thể sử dụng JDK đi kèm với Android Studio

### 3. Cấu hình biến môi trường
- **ANDROID_HOME**: Đường dẫn đến Android SDK
- **JAVA_HOME**: Đường dẫn đến JDK

## Các bước setup dự án

### Bước 1: Clone/Download dự án
```bash
# Nếu có Git
git clone <repository-url>
cd workly_android

# Hoặc download và giải nén vào thư mục workly_android
```

### Bước 2: Tạo file local.properties
1. Copy file `local.properties.template` thành `local.properties`
2. Sửa đường dẫn SDK trong file `local.properties`:
```properties
# Thay đổi đường dẫn này theo máy của bạn
sdk.dir=C\:\\Users\\YourUsername\\AppData\\Local\\Android\\Sdk

# Thêm API key cho OpenWeatherMap (tùy chọn)
OPENWEATHER_API_KEY=your_api_key_here
```

### Bước 3: Tạo Gradle Wrapper (nếu chưa có)
Nếu file `gradle/wrapper/gradle-wrapper.jar` chưa tồn tại:

**Cách 1: Sử dụng Android Studio**
1. Mở Android Studio
2. Chọn "Open an existing project"
3. Chọn thư mục `workly_android`
4. Android Studio sẽ tự động tạo Gradle wrapper

**Cách 2: Sử dụng command line**
```bash
# Nếu có Gradle đã cài đặt
gradle wrapper --gradle-version 8.4

# Hoặc tải manual
# Tải gradle-8.4-bin.zip từ https://gradle.org/releases/
# Giải nén và copy gradle-wrapper.jar vào gradle/wrapper/
```

### Bước 4: Sync và Build dự án

**Trong Android Studio:**
1. Mở dự án trong Android Studio
2. Chờ Gradle sync hoàn tất
3. Nếu có lỗi, click "Sync Now" hoặc "Try Again"
4. Build dự án: Build → Make Project

**Từ command line:**
```bash
# Windows
.\gradlew.bat build

# Linux/Mac
./gradlew build
```

### Bước 5: Chạy ứng dụng

**Trong Android Studio:**
1. Kết nối device Android hoặc khởi động emulator
2. Click nút "Run" (▶️) hoặc Shift+F10

**Từ command line:**
```bash
# Windows
.\gradlew.bat installDebug

# Linux/Mac
./gradlew installDebug
```

## Xử lý lỗi thường gặp

### Lỗi 1: "SDK location not found"
**Giải pháp:**
- Kiểm tra file `local.properties` có đúng đường dẫn SDK không
- Đảm bảo Android SDK đã được cài đặt

### Lỗi 2: "Gradle wrapper not found"
**Giải pháp:**
- Tạo Gradle wrapper như hướng dẫn ở Bước 3
- Hoặc mở dự án trong Android Studio để tự động tạo

### Lỗi 3: "Java version incompatible"
**Giải pháp:**
- Cài đặt JDK 17 hoặc mới hơn
- Trong Android Studio: File → Project Structure → SDK Location → JDK location

### Lỗi 4: "Build failed - dependencies not found"
**Giải pháp:**
- Kiểm tra kết nối internet
- Sync lại dự án: File → Sync Project with Gradle Files
- Clear cache: Build → Clean Project

### Lỗi 5: "Hilt compilation error"
**Giải pháp:**
- Đảm bảo đã thêm `@HiltAndroidApp` vào Application class
- Rebuild dự án: Build → Rebuild Project

## Cấu hình API Keys (Tùy chọn)

### OpenWeatherMap API
1. Đăng ký tài khoản tại: https://openweathermap.org/api
2. Lấy API key miễn phí
3. Thêm vào `local.properties`:
```properties
OPENWEATHER_API_KEY=your_actual_api_key_here
```

## Kiểm tra setup thành công

### 1. Build thành công
```bash
.\gradlew.bat build
# Kết quả: BUILD SUCCESSFUL
```

### 2. Chạy tests
```bash
.\gradlew.bat test
# Kết quả: All tests pass
```

### 3. Cài đặt trên device
```bash
.\gradlew.bat installDebug
# Kết quả: App installed successfully
```

### 4. Mở ứng dụng
- Ứng dụng "Workly" xuất hiện trên device
- Có thể mở và điều hướng giữa các tab
- Không có crash khi khởi động

## Cấu trúc thư mục sau khi setup

```
workly_android/
├── app/
│   ├── src/main/java/com/workly/app/
│   ├── src/main/res/
│   └── build.gradle.kts
├── gradle/
│   └── wrapper/
│       ├── gradle-wrapper.jar ✓
│       └── gradle-wrapper.properties ✓
├── build.gradle.kts ✓
├── settings.gradle.kts ✓
├── gradle.properties ✓
├── local.properties ✓ (tạo từ template)
├── gradlew.bat ✓
└── README.md ✓
```

## Hỗ trợ

Nếu gặp vấn đề trong quá trình setup:

1. **Kiểm tra log lỗi** trong Android Studio hoặc terminal
2. **Google search** thông báo lỗi cụ thể
3. **Stack Overflow** thường có giải pháp cho các lỗi Android phổ biến
4. **Android Developer Documentation**: https://developer.android.com/

## Next Steps

Sau khi setup thành công:
1. Đọc `README.md` để hiểu cấu trúc dự án
2. Xem code trong `app/src/main/java/com/workly/app/`
3. Customize theo nhu cầu của bạn
4. Thêm tính năng mới hoặc sửa đổi UI

---

**Chúc bạn setup thành công! 🚀**
