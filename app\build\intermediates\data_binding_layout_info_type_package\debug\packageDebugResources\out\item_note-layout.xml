<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_note" modulePackage="com.workly.app" filePath="app\src\main\res\layout\item_note.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_note_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="129" endOffset="51"/></Target><Target id="@+id/view_priority_indicator" view="View"><Expressions/><location startLine="17" startOffset="8" endLine="26" endOffset="40"/></Target><Target id="@+id/text_view_title" view="TextView"><Expressions/><location startLine="29" startOffset="8" endLine="41" endOffset="47"/></Target><Target id="@+id/text_view_content" view="TextView"><Expressions/><location startLine="44" startOffset="8" endLine="58" endOffset="93"/></Target><Target id="@+id/text_view_date" view="TextView"><Expressions/><location startLine="61" startOffset="8" endLine="73" endOffset="43"/></Target><Target id="@+id/text_view_reminder" view="TextView"><Expressions/><location startLine="76" startOffset="8" endLine="92" endOffset="40"/></Target><Target id="@+id/text_view_shift_count" view="TextView"><Expressions/><location startLine="95" startOffset="8" endLine="111" endOffset="40"/></Target><Target id="@+id/button_snooze" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="114" startOffset="8" endLine="125" endOffset="40"/></Target></Targets></Layout>