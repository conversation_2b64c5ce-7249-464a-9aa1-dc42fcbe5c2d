[{"merged": "com.workly.app-mergeDebugResources-62:/layout/item_note.xml", "source": "com.workly.app-main-65:/layout/item_note.xml"}, {"merged": "com.workly.app-mergeDebugResources-62:/layout/item_attendance_log.xml", "source": "com.workly.app-main-65:/layout/item_attendance_log.xml"}, {"merged": "com.workly.app-mergeDebugResources-62:/layout/fragment_shifts.xml", "source": "com.workly.app-main-65:/layout/fragment_shifts.xml"}, {"merged": "com.workly.app-mergeDebugResources-62:/layout/fragment_settings.xml", "source": "com.workly.app-main-65:/layout/fragment_settings.xml"}, {"merged": "com.workly.app-mergeDebugResources-62:/layout/fragment_home.xml", "source": "com.workly.app-main-65:/layout/fragment_home.xml"}, {"merged": "com.workly.app-mergeDebugResources-62:/layout/activity_main.xml", "source": "com.workly.app-main-65:/layout/activity_main.xml"}, {"merged": "com.workly.app-mergeDebugResources-62:/layout/fragment_statistics.xml", "source": "com.workly.app-main-65:/layout/fragment_statistics.xml"}, {"merged": "com.workly.app-mergeDebugResources-62:/layout/fragment_notes.xml", "source": "com.workly.app-main-65:/layout/fragment_notes.xml"}]