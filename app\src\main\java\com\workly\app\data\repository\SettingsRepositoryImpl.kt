package com.workly.app.data.repository

import android.content.SharedPreferences
import com.google.gson.Gson
import com.workly.app.data.model.UserSettings
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class SettingsRepositoryImpl @Inject constructor(
    private val sharedPreferences: SharedPreferences,
    private val gson: Gson
) : SettingsRepository {

    private val _userSettings = MutableStateFlow(loadUserSettings())
    
    override fun getUserSettings(): Flow<UserSettings> {
        return _userSettings.asStateFlow()
    }

    override suspend fun updateUserSettings(settings: UserSettings) {
        saveUserSettings(settings)
        _userSettings.value = settings
    }

    override suspend fun resetToDefaults() {
        val defaultSettings = UserSettings()
        updateUserSettings(defaultSettings)
    }

    override suspend fun exportSettings(): String {
        return gson.toJson(_userSettings.value)
    }

    override suspend fun importSettings(settingsJson: String): Boolean {
        return try {
            val settings = gson.fromJson(settingsJson, UserSettings::class.java)
            updateUserSettings(settings)
            true
        } catch (e: Exception) {
            false
        }
    }

    private fun loadUserSettings(): UserSettings {
        val settingsJson = sharedPreferences.getString(SETTINGS_KEY, null)
        return if (settingsJson != null) {
            try {
                gson.fromJson(settingsJson, UserSettings::class.java)
            } catch (e: Exception) {
                UserSettings() // Return default settings if parsing fails
            }
        } else {
            UserSettings() // Return default settings if no saved settings
        }
    }

    private fun saveUserSettings(settings: UserSettings) {
        val settingsJson = gson.toJson(settings)
        sharedPreferences.edit()
            .putString(SETTINGS_KEY, settingsJson)
            .apply()
    }

    companion object {
        private const val SETTINGS_KEY = "user_settings"
    }
}
