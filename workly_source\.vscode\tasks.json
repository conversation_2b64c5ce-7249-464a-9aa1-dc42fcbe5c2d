{"version": "2.0.0", "tasks": [{"label": "Start Expo", "type": "shell", "command": "npx expo start", "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "Start Expo Web", "type": "shell", "command": "npx expo start --web", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "Install Dependencies", "type": "shell", "command": "npm install", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "Generate Icons", "type": "shell", "command": "npm run generate-icons", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}, {"label": "Build Production", "type": "shell", "command": "eas build --profile production", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}]}