[{"merged": "com.workly.app-debug-63:/layout_fragment_home.xml.flat", "source": "com.workly.app-main-65:/layout/fragment_home.xml"}, {"merged": "com.workly.app-debug-63:/mipmap-xxhdpi_ic_launcher_round.xml.flat", "source": "com.workly.app-main-65:/mipmap-xxhdpi/ic_launcher_round.xml"}, {"merged": "com.workly.app-debug-63:/layout_fragment_settings.xml.flat", "source": "com.workly.app-main-65:/layout/fragment_settings.xml"}, {"merged": "com.workly.app-debug-63:/menu_bottom_nav_menu.xml.flat", "source": "com.workly.app-main-65:/menu/bottom_nav_menu.xml"}, {"merged": "com.workly.app-debug-63:/drawable_ic_launcher_background.xml.flat", "source": "com.workly.app-main-65:/drawable/ic_launcher_background.xml"}, {"merged": "com.workly.app-debug-63:/drawable_bg_chip_outline.xml.flat", "source": "com.workly.app-main-65:/drawable/bg_chip_outline.xml"}, {"merged": "com.workly.app-debug-63:/layout_item_attendance_log.xml.flat", "source": "com.workly.app-main-65:/layout/item_attendance_log.xml"}, {"merged": "com.workly.app-debug-63:/mipmap-hdpi_ic_launcher.xml.flat", "source": "com.workly.app-main-65:/mipmap-hdpi/ic_launcher.xml"}, {"merged": "com.workly.app-debug-63:/xml_data_extraction_rules.xml.flat", "source": "com.workly.app-main-65:/xml/data_extraction_rules.xml"}, {"merged": "com.workly.app-debug-63:/layout_fragment_statistics.xml.flat", "source": "com.workly.app-main-65:/layout/fragment_statistics.xml"}, {"merged": "com.workly.app-debug-63:/drawable_ic_sunny.xml.flat", "source": "com.workly.app-main-65:/drawable/ic_sunny.xml"}, {"merged": "com.workly.app-debug-63:/mipmap-xhdpi_ic_launcher.xml.flat", "source": "com.workly.app-main-65:/mipmap-xhdpi/ic_launcher.xml"}, {"merged": "com.workly.app-debug-63:/drawable_ic_notification.xml.flat", "source": "com.workly.app-main-65:/drawable/ic_notification.xml"}, {"merged": "com.workly.app-debug-63:/mipmap-xxxhdpi_ic_launcher.xml.flat", "source": "com.workly.app-main-65:/mipmap-xxxhdpi/ic_launcher.xml"}, {"merged": "com.workly.app-debug-63:/layout_fragment_notes.xml.flat", "source": "com.workly.app-main-65:/layout/fragment_notes.xml"}, {"merged": "com.workly.app-debug-63:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "com.workly.app-main-65:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "com.workly.app-debug-63:/drawable_ic_location_on_16.xml.flat", "source": "com.workly.app-main-65:/drawable/ic_location_on_16.xml"}, {"merged": "com.workly.app-debug-63:/drawable_ic_settings_24.xml.flat", "source": "com.workly.app-main-65:/drawable/ic_settings_24.xml"}, {"merged": "com.workly.app-debug-63:/mipmap-xxhdpi_ic_launcher.xml.flat", "source": "com.workly.app-main-65:/mipmap-xxhdpi/ic_launcher.xml"}, {"merged": "com.workly.app-debug-63:/drawable_ic_launcher_foreground.xml.flat", "source": "com.workly.app-main-65:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.workly.app-debug-63:/drawable_ic_note_24.xml.flat", "source": "com.workly.app-main-65:/drawable/ic_note_24.xml"}, {"merged": "com.workly.app-debug-63:/drawable_ic_schedule_24.xml.flat", "source": "com.workly.app-main-65:/drawable/ic_schedule_24.xml"}, {"merged": "com.workly.app-debug-63:/mipmap-mdpi_ic_launcher_round.xml.flat", "source": "com.workly.app-main-65:/mipmap-mdpi/ic_launcher_round.xml"}, {"merged": "com.workly.app-debug-63:/mipmap-mdpi_ic_launcher.xml.flat", "source": "com.workly.app-main-65:/mipmap-mdpi/ic_launcher.xml"}, {"merged": "com.workly.app-debug-63:/drawable_ic_alarm_16.xml.flat", "source": "com.workly.app-main-65:/drawable/ic_alarm_16.xml"}, {"merged": "com.workly.app-debug-63:/layout_item_note.xml.flat", "source": "com.workly.app-main-65:/layout/item_note.xml"}, {"merged": "com.workly.app-debug-63:/mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "com.workly.app-main-65:/mipmap-anydpi-v26/ic_launcher_round.xml"}, {"merged": "com.workly.app-debug-63:/drawable_ic_schedule_16.xml.flat", "source": "com.workly.app-main-65:/drawable/ic_schedule_16.xml"}, {"merged": "com.workly.app-debug-63:/drawable_ic_analytics_24.xml.flat", "source": "com.workly.app-main-65:/drawable/ic_analytics_24.xml"}, {"merged": "com.workly.app-debug-63:/navigation_mobile_navigation.xml.flat", "source": "com.workly.app-main-65:/navigation/mobile_navigation.xml"}, {"merged": "com.workly.app-debug-63:/drawable_ic_note_16.xml.flat", "source": "com.workly.app-main-65:/drawable/ic_note_16.xml"}, {"merged": "com.workly.app-debug-63:/mipmap-hdpi_ic_launcher_round.xml.flat", "source": "com.workly.app-main-65:/mipmap-hdpi/ic_launcher_round.xml"}, {"merged": "com.workly.app-debug-63:/mipmap-xxxhdpi_ic_launcher_round.xml.flat", "source": "com.workly.app-main-65:/mipmap-xxxhdpi/ic_launcher_round.xml"}, {"merged": "com.workly.app-debug-63:/layout_fragment_shifts.xml.flat", "source": "com.workly.app-main-65:/layout/fragment_shifts.xml"}, {"merged": "com.workly.app-debug-63:/drawable_ic_home_24.xml.flat", "source": "com.workly.app-main-65:/drawable/ic_home_24.xml"}, {"merged": "com.workly.app-debug-63:/xml_backup_rules.xml.flat", "source": "com.workly.app-main-65:/xml/backup_rules.xml"}, {"merged": "com.workly.app-debug-63:/layout_activity_main.xml.flat", "source": "com.workly.app-main-65:/layout/activity_main.xml"}, {"merged": "com.workly.app-debug-63:/mipmap-xhdpi_ic_launcher_round.xml.flat", "source": "com.workly.app-main-65:/mipmap-xhdpi/ic_launcher_round.xml"}]