// Generated by view binder compiler. Do not edit!
package com.workly.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.widget.NestedScrollView;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.workly.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentHomeBinding implements ViewBinding {
  @NonNull
  private final NestedScrollView rootView;

  @NonNull
  public final MaterialButton buttonMultiFunction;

  @NonNull
  public final MaterialCardView cardCurrentStatus;

  @NonNull
  public final MaterialCardView cardWeather;

  @NonNull
  public final ImageView imageViewWeatherIcon;

  @NonNull
  public final ProgressBar progressBarLoading;

  @NonNull
  public final RecyclerView recyclerViewAttendanceHistory;

  @NonNull
  public final RecyclerView recyclerViewNotes;

  @NonNull
  public final TextView textViewAttendanceTitle;

  @NonNull
  public final TextView textViewCurrentStatus;

  @NonNull
  public final TextView textViewCurrentTime;

  @NonNull
  public final TextView textViewNotesTitle;

  @NonNull
  public final TextView textViewWeatherDescription;

  @NonNull
  public final TextView textViewWeatherLocation;

  @NonNull
  public final TextView textViewWeatherTemp;

  private FragmentHomeBinding(@NonNull NestedScrollView rootView,
      @NonNull MaterialButton buttonMultiFunction, @NonNull MaterialCardView cardCurrentStatus,
      @NonNull MaterialCardView cardWeather, @NonNull ImageView imageViewWeatherIcon,
      @NonNull ProgressBar progressBarLoading, @NonNull RecyclerView recyclerViewAttendanceHistory,
      @NonNull RecyclerView recyclerViewNotes, @NonNull TextView textViewAttendanceTitle,
      @NonNull TextView textViewCurrentStatus, @NonNull TextView textViewCurrentTime,
      @NonNull TextView textViewNotesTitle, @NonNull TextView textViewWeatherDescription,
      @NonNull TextView textViewWeatherLocation, @NonNull TextView textViewWeatherTemp) {
    this.rootView = rootView;
    this.buttonMultiFunction = buttonMultiFunction;
    this.cardCurrentStatus = cardCurrentStatus;
    this.cardWeather = cardWeather;
    this.imageViewWeatherIcon = imageViewWeatherIcon;
    this.progressBarLoading = progressBarLoading;
    this.recyclerViewAttendanceHistory = recyclerViewAttendanceHistory;
    this.recyclerViewNotes = recyclerViewNotes;
    this.textViewAttendanceTitle = textViewAttendanceTitle;
    this.textViewCurrentStatus = textViewCurrentStatus;
    this.textViewCurrentTime = textViewCurrentTime;
    this.textViewNotesTitle = textViewNotesTitle;
    this.textViewWeatherDescription = textViewWeatherDescription;
    this.textViewWeatherLocation = textViewWeatherLocation;
    this.textViewWeatherTemp = textViewWeatherTemp;
  }

  @Override
  @NonNull
  public NestedScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentHomeBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentHomeBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_home, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentHomeBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button_multi_function;
      MaterialButton buttonMultiFunction = ViewBindings.findChildViewById(rootView, id);
      if (buttonMultiFunction == null) {
        break missingId;
      }

      id = R.id.card_current_status;
      MaterialCardView cardCurrentStatus = ViewBindings.findChildViewById(rootView, id);
      if (cardCurrentStatus == null) {
        break missingId;
      }

      id = R.id.card_weather;
      MaterialCardView cardWeather = ViewBindings.findChildViewById(rootView, id);
      if (cardWeather == null) {
        break missingId;
      }

      id = R.id.image_view_weather_icon;
      ImageView imageViewWeatherIcon = ViewBindings.findChildViewById(rootView, id);
      if (imageViewWeatherIcon == null) {
        break missingId;
      }

      id = R.id.progress_bar_loading;
      ProgressBar progressBarLoading = ViewBindings.findChildViewById(rootView, id);
      if (progressBarLoading == null) {
        break missingId;
      }

      id = R.id.recycler_view_attendance_history;
      RecyclerView recyclerViewAttendanceHistory = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewAttendanceHistory == null) {
        break missingId;
      }

      id = R.id.recycler_view_notes;
      RecyclerView recyclerViewNotes = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewNotes == null) {
        break missingId;
      }

      id = R.id.text_view_attendance_title;
      TextView textViewAttendanceTitle = ViewBindings.findChildViewById(rootView, id);
      if (textViewAttendanceTitle == null) {
        break missingId;
      }

      id = R.id.text_view_current_status;
      TextView textViewCurrentStatus = ViewBindings.findChildViewById(rootView, id);
      if (textViewCurrentStatus == null) {
        break missingId;
      }

      id = R.id.text_view_current_time;
      TextView textViewCurrentTime = ViewBindings.findChildViewById(rootView, id);
      if (textViewCurrentTime == null) {
        break missingId;
      }

      id = R.id.text_view_notes_title;
      TextView textViewNotesTitle = ViewBindings.findChildViewById(rootView, id);
      if (textViewNotesTitle == null) {
        break missingId;
      }

      id = R.id.text_view_weather_description;
      TextView textViewWeatherDescription = ViewBindings.findChildViewById(rootView, id);
      if (textViewWeatherDescription == null) {
        break missingId;
      }

      id = R.id.text_view_weather_location;
      TextView textViewWeatherLocation = ViewBindings.findChildViewById(rootView, id);
      if (textViewWeatherLocation == null) {
        break missingId;
      }

      id = R.id.text_view_weather_temp;
      TextView textViewWeatherTemp = ViewBindings.findChildViewById(rootView, id);
      if (textViewWeatherTemp == null) {
        break missingId;
      }

      return new FragmentHomeBinding((NestedScrollView) rootView, buttonMultiFunction,
          cardCurrentStatus, cardWeather, imageViewWeatherIcon, progressBarLoading,
          recyclerViewAttendanceHistory, recyclerViewNotes, textViewAttendanceTitle,
          textViewCurrentStatus, textViewCurrentTime, textViewNotesTitle,
          textViewWeatherDescription, textViewWeatherLocation, textViewWeatherTemp);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
